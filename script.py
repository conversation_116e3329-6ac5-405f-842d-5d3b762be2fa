import os
import subprocess
import re
import shutil
import sys
import logging
import argparse
from pathlib import Path
import concurrent.futures
import psutil
from threading import Lock, Event
import json
from datetime import datetime
import time
from video_utils import get_video_info, build_ffmpeg_command
import locale
import signal
import atexit
from collections import deque
try:
    from performance_monitor import start_performance_monitoring, stop_performance_monitoring, get_performance_report
    PERFORMANCE_MONITORING_AVAILABLE = True
except ImportError:
    PERFORMANCE_MONITORING_AVAILABLE = False
    print("性能监控模块不可用，将跳过性能监控功能")

try:
    from progress_monitor import ProgressManager, ProgressData
    progress_manager = ProgressManager()
    PROGRESS_MONITOR_AVAILABLE = True
except ImportError:
    PROGRESS_MONITOR_AVAILABLE = False
    print("高精度进度监控模块不可用，将使用兼容模式")

# 用于存储所有ffmpeg进程的列表
ffmpeg_processes = []
ffmpeg_processes_lock = Lock()

# 进度更新队列和控制
progress_update_queue = deque(maxlen=100)
progress_update_lock = Lock()
last_progress_update = 0
PROGRESS_UPDATE_INTERVAL = 0.05  # 50ms更新间隔，提高进度条流畅度

# 内存管理优化
log_buffer = deque(maxlen=1000)  # 限制日志缓冲区大小
log_cleanup_counter = 0
LOG_CLEANUP_INTERVAL = 100  # 每100次操作清理一次

def cleanup_processes():
    """清理所有ffmpeg进程"""
    with ffmpeg_processes_lock:
        for proc in ffmpeg_processes[:]:  # 使用切片创建副本以避免在迭代时修改列表
            try:
                if proc.is_running():
                    proc.terminate()
                ffmpeg_processes.remove(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied, ValueError):
                pass

def signal_handler(signum, frame):
    """处理中断信号"""
    cleanup_processes()

# 注册清理函数和信号处理
atexit.register(cleanup_processes)
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def set_terminal_encoding():
    """根据操作系统设置终端编码为 UTF-8，避免输出乱码。"""
    if sys.platform == "win32":
        try:
            import ctypes
            ctypes.windll.kernel32.SetConsoleOutputCP(65001)
        except Exception as e:
            print(f"无法设置 Windows 终端编码为 UTF-8: {e}", file=sys.stderr)
    elif sys.platform in ("linux", "darwin"):
        try:
            locale.setlocale(locale.LC_ALL, '')
            sys.stdout.reconfigure(encoding='utf-8')
        except Exception as e:
            print(f"无法设置 {sys.platform.capitalize()} 终端编码为 UTF-8: {e}", file=sys.stderr)

class SafeStreamHandler(logging.StreamHandler):
    """确保日志消息在特殊字符转义后被正确写入终端。"""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.buffer = []
        self.buffer_size = 1000  # 最多保留1000条日志

    def emit(self, record):
        try:
            msg = self.format(record)
            if isinstance(msg, str):
                msg = msg.encode('utf-8', errors='replace').decode('utf-8')
            
            # 添加到缓冲区
            self.buffer.append(msg)
            if len(self.buffer) > self.buffer_size:
                self.buffer.pop(0)  # 移除最旧的日志
            
            self.stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)

def setup_logging():
    """配置日志记录，设置日志级别和格式，并添加流处理器，包含内存优化。"""
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    formatter = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")
    
    # 添加文件处理器，限制文件大小
    from logging.handlers import RotatingFileHandler
    log_file = 'video_transcode.log'
    file_handler = RotatingFileHandler(
        log_file, 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=3,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    stream_handler = SafeStreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

def check_dependencies():
    """检查必要的命令行工具（ffmpeg 和 ffprobe）是否可用。"""
    from ffmpeg_utils import get_ffmpeg_path, get_ffprobe_path
    import os

    missing_deps = []
    ffmpeg_path = get_ffmpeg_path()
    ffprobe_path = get_ffprobe_path()

    if not os.path.exists(ffmpeg_path):
        missing_deps.append('ffmpeg')
    if not os.path.exists(ffprobe_path):
        missing_deps.append('ffprobe')

    if missing_deps:
        error_msg = f"以下命令未找到: {', '.join(missing_deps)}。程序无法继续运行。"
        logging.error(error_msg)
        raise RuntimeError(error_msg)

def parse_arguments():
    """解析命令行参数，提供用户可配置的选项。"""
    parser = argparse.ArgumentParser(description="批量转码视频文件")
    parser.add_argument('--input_dir', type=str, default='input', help='输入文件夹路径')
    parser.add_argument('--output_dir', type=str, default='output', help='输出文件夹路径')
    parser.add_argument('--video_format', type=str, help='输出视频格式')
    parser.add_argument('--resolution', type=str, help='视频分辨率')
    parser.add_argument('--video_bitrate', type=str, help='视频比特率')
    parser.add_argument('--audio_bitrate', type=str, help='音频比特率')
    parser.add_argument('--overwrite', action='store_true', help='是否覆盖已存在的文件')
    parser.add_argument('--min_size', type=float, default=0, help='最小文件大小（MB）')
    parser.add_argument('--max_size', type=float, default=float('inf'), help='最大文件大小（MB）')
    parser.add_argument('--output_name', type=str, help='输出文件名模板')
    parser.add_argument('--priority', type=int, default=psutil.NORMAL_PRIORITY_CLASS, help='进程优先级')
    parser.add_argument('--use_gpu', action='store_true', help='是否使用GPU加速')
    parser.add_argument('--max_retries', type=int, default=3, help='转码失败时的最大重试次数')
    parser.add_argument('--keep_original_resolution', action='store_true', help='保持原视频分辨率')
    parser.add_argument('--auto_detect_videos', action='store_true', help='自动检测所有视频文件')
    return parser.parse_args()

def validate_arguments(args, config):
    """验证命令行参数和配置文件中的参数格式是否正确。"""
    if args.resolution and not re.match(r'\d+x\d+', args.resolution):
        raise ValueError("分辨率格式不正确，应为 'WIDTHxHEIGHT'")
    
    if args.video_bitrate and not re.match(r'\d+k', args.video_bitrate):
        raise ValueError("视频比特率格式不正确，应为 'NUMBERk'")
    
    if args.audio_bitrate and not re.match(r'\d+k', args.audio_bitrate):
        raise ValueError("音频比特率格式不正确，应为 'NUMBERk'")

def load_config():
    """加载配置文件，如果文件不存在则返回默认配置。"""
    config_path = Path('config.json')
    if config_path.exists():
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {
            "input_dir": "input",
            "output_dir": "output",
            "video_format": "mp4",
            "resolution": "1280x720",
            "video_bitrate": "2000k",
            "audio_bitrate": "128k",
            "overwrite": True,
            "use_gpu": False,
            "max_retries": 1,
            "supported_formats": [".wmv", ".avi", ".mp4", ".mkv"],
            "output_name": "{name}_{resolution}_{timestamp}",
            "min_size": 0,
            "max_size": 10240,
            "keep_original_resolution": False,
            "auto_detect_videos": False
        }
    return config

def save_config(config):
    """保存配置文件。"""
    with open('config.json', 'w') as f:
        json.dump(config, f, indent=4)

def format_video_info(video_info):
    """格式化视频信息为易读的字符串。"""
    if not video_info:
        return "无法获取视频信息"
    
    resolution = f"{video_info.get('width', 'N/A')}x{video_info.get('height', 'N/A')}"
    bitrate = f"{int(video_info.get('bitrate', 0)) // 1000}k" if video_info.get('bitrate') else "N/A"
    
    return f"分辨率: {resolution}, 码率: {bitrate}"

def _cleanup_memory():
    """定期清理内存，防止内存泄漏"""
    global log_cleanup_counter
    log_cleanup_counter += 1
    
    if log_cleanup_counter >= LOG_CLEANUP_INTERVAL:
        log_cleanup_counter = 0
        # 清理日志缓冲区
        if len(log_buffer) > 500:
            # 保留最新的500条记录
            while len(log_buffer) > 500:
                log_buffer.popleft()
        
        # 强制垃圾回收
        import gc
        gc.collect()

def _queue_progress_update(callback, progress, filename):
    """优化的批量进度更新机制，提高流畅度"""
    global last_progress_update
    current_time = time.time()
    
    with progress_update_lock:
        # 对于重要的进度节点（0%, 25%, 50%, 75%, 100%）立即更新
        important_progress = progress in [0.0, 0.25, 0.5, 0.75, 1.0] or abs(progress - 1.0) < 0.01
        
        progress_update_queue.append((callback, progress, filename, current_time))
        
        # 重要进度立即更新，或达到时间间隔时批量更新
        if important_progress or current_time - last_progress_update >= PROGRESS_UPDATE_INTERVAL:
            # 批量处理队列中的进度更新
            while progress_update_queue:
                cb, prog, fname, _ = progress_update_queue.popleft()
                try:
                    cb(prog, fname)
                except Exception as e:
                    logging.warning(f"进度更新回调失败: {e}")
            last_progress_update = current_time
            
            # 定期清理内存
            _cleanup_memory()

def process_single_video(video_file, config, output_path, progress_callback=None, pause_event=None):
    """处理单个视频文件，使用高精度进度监控"""
    while pause_event and pause_event.is_set():
        time.sleep(0.1)
    
    # 阶段1: 初始化进度反馈 (0%)
    if progress_callback:
        _queue_progress_update(progress_callback, 0.0, video_file.name)
        logging.info(f"开始处理视频: {video_file.name}")
        
    # 阶段2: 分析视频信息 (5%)
    if progress_callback:
        _queue_progress_update(progress_callback, 0.05, video_file.name)
        logging.info(f"正在分析视频信息: {video_file.name}")
        
    video_info = get_video_info(video_file)
    if not video_info:
        logging.error(f"无法获取视频 '{video_file.name}' 的信息，跳过处理。")
        return False

    # 阶段3: 视频信息分析完成 (10%)
    if progress_callback:
        _queue_progress_update(progress_callback, 0.10, video_file.name)
        
    logging.info(f"原始视频 '{video_file.name}' 信息：{format_video_info(video_info)}")

    output_format = config['video_format'].lower()
    if not output_format.startswith('.'):
        output_format = '.' + output_format

    # 处理保持原分辨率的情况
    if config.get('keep_original_resolution', False):
        config['resolution'] = f"{video_info['width']}x{video_info['height']}"
        config['video_bitrate'] = f"{int(video_info['bitrate']) // 1000}k" if video_info['bitrate'] else config['video_bitrate']
        logging.info(f"保持原视频质量：分辨率 {config['resolution']}，码率 {config['video_bitrate']}")

    # 阶段4: 准备输出配置 (12%)
    if progress_callback:
        _queue_progress_update(progress_callback, 0.12, video_file.name)
        logging.info(f"正在准备转码参数: {video_file.name}")

    output_name = config['output_name'].format(
        name=video_file.stem,
        resolution=config['resolution'],
        timestamp=datetime.now().strftime("%Y%m%d_%H%M%S")
    )
    output_file = output_path / f"{output_name}{output_format}"

    if output_file.exists() and not config['overwrite']:
        logging.info(f"输出文件 '{output_file}' 已存在，跳过。")
        return False

    # 阶段5: 构建FFmpeg命令 (15%)
    if progress_callback:
        _queue_progress_update(progress_callback, 0.15, video_file.name)
        logging.info(f"正在构建转码命令: {video_file.name}")
        
    command = build_ffmpeg_command(video_file, config, output_file, video_info)

    # 阶段6: 启动转码进程 (18%)
    if progress_callback:
        _queue_progress_update(progress_callback, 0.18, video_file.name)
        logging.info(f"正在启动FFmpeg转码进程: {video_file.name}")

    # 使用高精度进度监控
    if PROGRESS_MONITOR_AVAILABLE and progress_callback:
        progress_manager.start_monitoring(video_file.name, video_info['duration'])
        
        def on_progress(progress_data):
            if progress_callback:
                progress_callback(progress_data.progress, progress_data.file_name)
        
        progress_manager.add_callback(on_progress)

    # 优化的错误处理和重试机制
    last_error = None
    for attempt in range(config['max_retries']):
        try:
            with subprocess.Popen(
                command,
                creationflags=subprocess.CREATE_NO_WINDOW,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace'
            ) as process:
                psutil.Process(process.pid).nice(config['priority'])
                
                # 阶段7: FFmpeg进程已启动，开始转码 (20%)
                if progress_callback:
                    _queue_progress_update(progress_callback, 0.20, video_file.name)
                    logging.info(f"FFmpeg转码进程已启动，开始转码: {video_file.name}")
                
                ffmpeg_process = psutil.Process(process.pid)
                with ffmpeg_processes_lock:
                    ffmpeg_processes.append(ffmpeg_process)
                
                try:
                    # 使用高精度进度解析
                    if PROGRESS_MONITOR_AVAILABLE:
                        for line in process.stderr:
                            # 检查暂停状态
                            if pause_event and pause_event.is_set():
                                try:
                                    psutil.Process(process.pid).suspend()
                                    pause_event.wait()
                                    psutil.Process(process.pid).resume()
                                except (psutil.NoSuchProcess, psutil.AccessDenied):
                                    pass
                            
                            # 解析进度
                            progress_manager.parse_progress(video_file.name, line)
                    else:
                        # 兼容模式：使用原有逻辑
                        time_pattern = re.compile(r'time=(\d{1,2}):(\d{2}):(\d{2}(?:\.\d+)?)')
                        for line in process.stderr:
                            if pause_event and pause_event.is_set():
                                try:
                                    psutil.Process(process.pid).suspend()
                                    pause_event.wait()
                                    psutil.Process(process.pid).resume()
                                except (psutil.NoSuchProcess, psutil.AccessDenied):
                                    pass
                            
                            match = time_pattern.search(line)
                            if match:
                                hours, minutes, seconds = match.groups()
                                current_time = float(hours) * 3600 + float(minutes) * 60 + float(seconds)
                                if progress_callback and video_info['duration']:
                                    file_progress = min(current_time / video_info['duration'], 1.0)
                                    _queue_progress_update(progress_callback, file_progress, video_file.name)

                    if process.wait() != 0:
                        error_msg = f"FFmpeg进程退出码: {process.returncode}"
                        raise subprocess.CalledProcessError(process.returncode, command, error_msg)
                        
                finally:
                    # 清理进度监控
                    if PROGRESS_MONITOR_AVAILABLE:
                        progress_manager.stop_monitoring(video_file.name)
                        progress_manager.remove_callback(on_progress)
                    
                    # 确保进程从列表中移除
                    with ffmpeg_processes_lock:
                        try:
                            if ffmpeg_process in ffmpeg_processes:
                                ffmpeg_processes.remove(ffmpeg_process)
                        except ValueError:
                            pass

            logging.info(f"转码 '{video_file.name}' 成功。")
            
            # 获取并显示转换后的视频信息
            output_video_info = get_video_info(output_file)
            if output_video_info:
                logging.info(f"转换后视频 '{output_file.name}' 信息：{format_video_info(output_video_info)}")
                
                # 计算并显示码率变化
                original_bitrate = int(video_info.get('bitrate', 0)) // 1000
                new_bitrate = int(output_video_info.get('bitrate', 0)) // 1000
                bitrate_change = new_bitrate - original_bitrate
                bitrate_change_percent = (bitrate_change / original_bitrate) * 100 if original_bitrate > 0 else 0
                
                logging.info(f"码率变化: {bitrate_change:+d}k ({bitrate_change_percent:.2f}%)")
            else:
                logging.warning(f"无法获取转换后视频 '{output_file.name}' 的信息。")
            
            return True

        except subprocess.CalledProcessError as e:
            last_error = f"FFmpeg错误 (退出码 {e.returncode})"
            logging.error(f"转码 '{video_file.name}' 失败（尝试 {attempt + 1}/{config['max_retries']}）- {last_error}")
        except Exception as e:
            last_error = str(e)
            logging.error(f"转码 '{video_file.name}' 时发生错误（尝试 {attempt + 1}/{config['max_retries']}）: {last_error}")
        
        # 只有在非最后一次尝试时才等待重试
        if attempt < config['max_retries'] - 1:
            retry_delay = min(2 ** attempt, 10)  # 指数退避，最大10秒
            time.sleep(retry_delay)
    
    # 所有重试都失败了
    logging.error(f"所有重试都失败: {video_file.name} - 最后错误: {last_error}")
    return False

def filter_videos(video_files, min_size, max_size):
    """过滤视频文件，根据最小和最大文件大小。"""
    return [
        f for f in video_files
        if min_size <= f.stat().st_size / (1024 * 1024) <= max_size
    ]

def transcode_videos_with_detailed_progress(config, progress_callback=None, pause_event=None):
    """批量转码视频文件，并通过回调函数更新进度，支持性能监控。"""
    if pause_event and pause_event.is_set():
        return

    input_path = Path(config['input_dir'])
    if not input_path.exists() or not input_path.is_dir():
        logging.error(f"输入文件夹 '{config['input_dir']}' 不存在或不是一个文件夹。")
        return

    output_path = Path(config['output_dir'])
    output_path.mkdir(parents=True, exist_ok=True)

    # 确保所有格式都是小写并且以点开头
    supported_formats = [fmt.lower() if fmt.startswith('.') else f'.{fmt.lower()}' for fmt in config['supported_formats']]
    
    # 使用ffprobe检测文件是否为有效视频文件
    def is_valid_video(file_path):
        from ffmpeg_utils import get_ffprobe_path
        try:
            cmd = [
                get_ffprobe_path(),
                '-v', 'error',
                '-select_streams', 'v:0',  # 选择第一个视频流
                '-show_entries', 'stream=codec_type,r_frame_rate,duration:format=duration',
                '-of', 'json',
                str(file_path)
            ]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
            
            if result.returncode != 0:
                return False
                
            import json
            info = json.loads(result.stdout)
            
            # 首先检查常见的静态图像格式扩展名
            file_ext = Path(file_path).suffix.lower()
            if file_ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp', '.ico', '.svg']:
                return False
            
            # 检查是否有视频流
            if not ('streams' in info and info['streams'] and info['streams'][0].get('codec_type') == 'video'):
                return False
            
            stream = info['streams'][0]
            format_info = info.get('format', {})
            
            # 检查持续时间 - 真正的视频文件应该有合理的持续时间
            duration = format_info.get('duration')
            if duration:
                try:
                    duration_float = float(duration)
                    if duration_float > 0.1:  # 持续时间大于0.1秒认为是视频
                        return True
                except ValueError:
                    pass
            
            # 检查帧率 - 但要排除可能的误报
            frame_rate = stream.get('r_frame_rate', '0/1')
            if frame_rate and frame_rate != '0/1':
                try:
                    # 解析帧率 (例如 "25/1" -> 25.0)
                    num, den = map(float, frame_rate.split('/'))
                    if den > 0 and num / den > 0:
                        # 有帧率但没有持续时间的可能是静态图像，需要额外检查
                        if not duration:
                            return False
                        return True  # 有有效帧率和持续时间，是视频
                except (ValueError, ZeroDivisionError):
                    pass
            
            return False  # 默认不认为是有效视频
        except Exception:
            return False
    
    # 首先根据文件扩展名过滤
    video_files = [f for f in input_path.iterdir() if f.suffix.lower() in supported_formats]
    
    # 如果用户选择了"自动检测所有视频文件"选项
    if config.get('auto_detect_videos', False):
        # 添加没有扩展名或扩展名不在支持列表中但可能是视频的文件
        potential_videos = [f for f in input_path.iterdir() 
                           if f.is_file() and f not in video_files]
        
        # 使用ffprobe检测这些文件是否为视频
        for file in potential_videos:
            if is_valid_video(file):
                video_files.append(file)
                logging.info(f"自动检测到视频文件: {file.name}")
    
    video_files = filter_videos(video_files, config['min_size'], config['max_size'])

    if not video_files:
        error_msg = f"输入文件夹 '{config['input_dir']}' 中没有符合条件的视频文件。"
        logging.error(error_msg)
        raise RuntimeError(error_msg)

    total_files = len(video_files)
    completed_files = 0

    # 启动性能监控
    if PERFORMANCE_MONITORING_AVAILABLE:
        start_performance_monitoring()
        logging.info("性能监控已启动")

    # 创建一个字典来跟踪每个future对应的文件
    future_to_file = {}
    
    def update_progress(file_progress, current_file):
        nonlocal completed_files
        total_progress = (completed_files + file_progress) / total_files
        if progress_callback:
            # 确保传递的是文件路径字符串而不是文件对象
            file_path = str(current_file) if hasattr(current_file, '__str__') else current_file
            progress_callback(total_progress, file_progress, file_path)

    # 设置线程池大小为CPU核心数，通过单一控制机制管理并发
    max_workers = max(1, min(total_files, psutil.cpu_count(logical=False)))
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for video_file in video_files:
            future = executor.submit(
                process_single_video, 
                video_file, 
                config, 
                output_path, 
                lambda p, f=video_file: update_progress(p, f),  # 传递当前文件
                pause_event
            )
            future_to_file[future] = video_file
            futures.append(future)

        for future in concurrent.futures.as_completed(futures):
            if pause_event and pause_event.is_set():
                for f in futures:
                    f.cancel()
                break

            current_file = future_to_file[future]
            try:
                result = future.result()
                if result:
                    update_progress(1.0, current_file)  # 先更新为100%完成
                    completed_files += 1  # 然后再增加完成计数
                else:
                    update_progress(0, current_file)  # 如果跳过则显示0%
            except Exception as e:
                logging.error(f"处理文件 '{current_file.name}' 时发生错误: {e}")
            finally:
                future_to_file.pop(future, None)

    # 停止性能监控并生成报告
    if PERFORMANCE_MONITORING_AVAILABLE:
        stop_performance_monitoring()
        performance_report = get_performance_report()
        logging.info("性能监控报告:")
        for line in performance_report.split('\n'):
            if line.strip():
                logging.info(line)

    logging.info("所有转码任务完成。")

if __name__ == "__main__":
    set_terminal_encoding()
    setup_logging()
    check_dependencies()
    
    config = load_config()
    args = parse_arguments()
    
    # 从命令行参数中更新配置
    args_dict = vars(args)
    for key, value in args_dict.items():
        # 只有在命令行参数明确提供且不为 None 时才更新配置
        if value is not None and key != 'video_format':
            config[key] = value
    
    # 如果命令行提供了 video_format，则使用命令行的值；否则保持配置文件的值
    if args.video_format is not None:
        config['video_format'] = args.video_format
    
    try:
        validate_arguments(args, config)
    except ValueError as e:
        logging.error(f"参数验证失败: {e}")
        sys.exit(1)
    
    save_config(config)
    transcode_videos_with_detailed_progress(config)
