# 视频批量转换工具

## 简介

这是一个功能强大的视频批量转换工具，可以帮助您将多个视频文件转换为指定格式和分辨率。本工具具有智能码率调整功能，能够根据视频内容自动优化视频质量。

## 主要功能

- 批量转换视频文件，支持多种常见格式
- 智能码率调整，根据视频内容自动优化质量
- 可自定义分辨率和比特率，满足不同需求
- 支持GPU加速，大幅提高转换速度
- 保持原分辨率选项，保证视频原始质量
- 实时显示转换进度和详细日志
- 可暂停和继续转换过程，灵活控制

## 安装说明

### 方法一：直接使用打包好的可执行文件

1. 下载最新的发布版本
2. 解压缩下载的文件
3. 运行 `启动视频转换工具.bat` 或直接运行 `视频批量转换工具.exe`

### 方法二：从源代码构建

1. 确保已安装Python 3.6或更高版本
2. 克隆或下载本仓库
3. 安装依赖项：

   ```text
   pip install pyqt5 psutil pillow
   ```

4. 运行打包脚本：

   ```text
   build.bat
   ```

5. 打包完成后，可执行文件将生成在`dist`目录中

## 使用方法

1. 将需要转换的视频文件放入`input`文件夹
2. 启动程序，设置所需的转换参数
3. 点击"开始转换"按钮
4. 转换完成后，转换后的视频文件将保存在`output`文件夹中

## 系统要求

- Windows 7/8/10/11
- 至少2GB内存
- 足够的硬盘空间用于存储转换后的视频

## 注意事项

- 转换大型视频文件可能需要较长时间
- 使用GPU加速需要兼容的显卡和驱动
- 请确保有足够的磁盘空间用于输出文件

## 版本历史

- v1.1.0 - 界面美化，增加表格显示转换进度
- v1.0.0 - 初始版本

## 许可证

© 2023 视频批量转换工具
