#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控工具
用于监控视频转换过程中的系统资源使用情况
"""

import psutil
import time
import threading
import logging
from collections import deque
from datetime import datetime

class PerformanceMonitor:
    """性能监控类"""
    
    def __init__(self, monitor_interval=1.0):
        self.monitor_interval = monitor_interval
        self.monitoring = False
        self.monitor_thread = None
        
        # 性能数据存储（最多保存1000个数据点）
        self.cpu_usage = deque(maxlen=1000)
        self.memory_usage = deque(maxlen=1000)
        self.disk_io = deque(maxlen=1000)
        self.network_io = deque(maxlen=1000)
        self.timestamps = deque(maxlen=1000)
        
        # 统计数据
        self.peak_cpu = 0
        self.peak_memory = 0
        self.total_disk_read = 0
        self.total_disk_write = 0
        
        self.lock = threading.Lock()
        
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            logging.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if self.monitoring:
            self.monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=2)
            logging.info("性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        last_disk_io = psutil.disk_io_counters()
        
        while self.monitoring:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=None)
                
                # 内存使用率
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                
                # 磁盘I/O
                current_disk_io = psutil.disk_io_counters()
                if last_disk_io:
                    disk_read_speed = (current_disk_io.read_bytes - last_disk_io.read_bytes) / self.monitor_interval
                    disk_write_speed = (current_disk_io.write_bytes - last_disk_io.write_bytes) / self.monitor_interval
                else:
                    disk_read_speed = 0
                    disk_write_speed = 0
                
                # 网络I/O
                network = psutil.net_io_counters()
                
                # 记录数据
                with self.lock:
                    self.cpu_usage.append(cpu_percent)
                    self.memory_usage.append(memory_percent)
                    self.disk_io.append((disk_read_speed, disk_write_speed))
                    self.network_io.append((network.bytes_sent, network.bytes_recv))
                    self.timestamps.append(datetime.now())
                    
                    # 更新峰值
                    self.peak_cpu = max(self.peak_cpu, cpu_percent)
                    self.peak_memory = max(self.peak_memory, memory_percent)
                    
                    # 累计磁盘I/O
                    self.total_disk_read += disk_read_speed * self.monitor_interval
                    self.total_disk_write += disk_write_speed * self.monitor_interval
                
                last_disk_io = current_disk_io
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logging.error(f"性能监控出错: {e}")
                time.sleep(self.monitor_interval)
    
    def get_current_stats(self):
        """获取当前统计信息"""
        with self.lock:
            if not self.cpu_usage:
                return None
            
            # 获取当前磁盘I/O速度
            current_disk_read_speed = 0
            current_disk_write_speed = 0
            if self.disk_io:
                current_disk_read_speed, current_disk_write_speed = self.disk_io[-1]
            
            return {
                'current_cpu': self.cpu_usage[-1] if self.cpu_usage else 0,
                'current_memory': self.memory_usage[-1] if self.memory_usage else 0,
                'peak_cpu': self.peak_cpu,
                'peak_memory': self.peak_memory,
                'avg_cpu': sum(self.cpu_usage) / len(self.cpu_usage) if self.cpu_usage else 0,
                'avg_memory': sum(self.memory_usage) / len(self.memory_usage) if self.memory_usage else 0,
                'total_disk_read_mb': self.total_disk_read / (1024 * 1024),
                'total_disk_write_mb': self.total_disk_write / (1024 * 1024),
                'disk_read_speed': current_disk_read_speed,
                'disk_write_speed': current_disk_write_speed,
                'data_points': len(self.cpu_usage)
            }
    
    def get_performance_report(self):
        """生成性能报告"""
        stats = self.get_current_stats()
        if not stats:
            return "暂无性能数据"
        
        report = f"""
性能监控报告
==================
当前CPU使用率: {stats['current_cpu']:.1f}%
当前内存使用率: {stats['current_memory']:.1f}%
峰值CPU使用率: {stats['peak_cpu']:.1f}%
峰值内存使用率: {stats['peak_memory']:.1f}%
平均CPU使用率: {stats['avg_cpu']:.1f}%
平均内存使用率: {stats['avg_memory']:.1f}%
总磁盘读取: {stats['total_disk_read_mb']:.1f} MB
总磁盘写入: {stats['total_disk_write_mb']:.1f} MB
数据点数量: {stats['data_points']}
==================
"""
        return report
    
    def reset_stats(self):
        """重置统计数据"""
        with self.lock:
            self.cpu_usage.clear()
            self.memory_usage.clear()
            self.disk_io.clear()
            self.network_io.clear()
            self.timestamps.clear()
            self.peak_cpu = 0
            self.peak_memory = 0
            self.total_disk_read = 0
            self.total_disk_write = 0

# 全局性能监控实例
performance_monitor = PerformanceMonitor()

def start_performance_monitoring():
    """启动性能监控"""
    performance_monitor.start_monitoring()

def stop_performance_monitoring():
    """停止性能监控"""
    performance_monitor.stop_monitoring()

def get_performance_stats():
    """获取性能统计"""
    return performance_monitor.get_current_stats()

def get_performance_report():
    """获取性能报告"""
    return performance_monitor.get_performance_report()

def reset_performance_stats():
    """重置性能统计"""
    performance_monitor.reset_stats()