import sys
import json
import time
from pathlib import Path
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QLabel, QLineEdit, QPushButton, QFileDialog, 
                           QComboBox, QCheckBox, QSpinBox, QProgressBar, QTextEdit, 
                           QScrollArea, QFrame, QStyleFactory, QTabWidget, QTableWidget,
                           QTableWidgetItem, QHeaderView, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QTextCursor, QIcon, QFont, QColor, QPixmap
import script
import atexit

# 导入性能监控模块
try:
    from performance_monitor import performance_monitor
    PERFORMANCE_MONITORING_AVAILABLE = True
except ImportError:
    PERFORMANCE_MONITORING_AVAILABLE = False
    print("性能监控模块不可用")

print("Starting GUI application...")

# 全局样式表定义
GLOBAL_STYLE = """
QMainWindow, QWidget {
    background-color: #f8f9fa;
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
}

QLabel {
    color: #2c3e50;
    font-size: 13px;
}

QLineEdit, QComboBox, QSpinBox {
    border: 1px solid #dcdde1;
    border-radius: 6px;
    padding: 8px;
    background-color: white;
    selection-background-color: #00b894;
    font-size: 13px;
    min-height: 18px;
}

QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
    border: 1px solid #00b894;
    border-width: 2px;
}

QPushButton {
    border: none;
    border-radius: 6px;
    padding: 10px 18px;
    background-color: #dcdde1;
    color: #2c3e50;
    font-size: 13px;
    font-weight: 500;
}

QPushButton:hover {
    background-color: #c8c9cc;
}

QPushButton:pressed {
    background-color: #b2b3b6;
}

QCheckBox {
    spacing: 8px;
    color: #2c3e50;
    font-size: 13px;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
    border: 1px solid #dcdde1;
    border-radius: 4px;
}

QCheckBox::indicator:checked {
    background-color: #00b894;
    border: 1px solid #00b894;
    image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cG9seWxpbmUgcG9pbnRzPSIyMCA2IDkgMTcgNCAxMiI+PC9wb2x5bGluZT48L3N2Zz4=);
}

QProgressBar {
    border: none;
    border-radius: 6px;
    text-align: center;
    background-color: #f0f0f0;
    height: 24px;
    font-weight: bold;
    color: #2c3e50;
}

QProgressBar::chunk {
    background-color: #00b894;
    border-radius: 6px;
}

QTextEdit {
    border: 1px solid #dcdde1;
    border-radius: 6px;
    background-color: white;
    font-family: "Consolas", "Microsoft YaHei", monospace;
    font-size: 13px;
}

QTableWidget {
    border: none;
    background-color: white;
    gridline-color: #f0f0f0;
    border-radius: 6px;
    selection-background-color: #e6f7f2;
    selection-color: #2c3e50;
}

QHeaderView::section {
    background-color: #f5f6fa;
    padding: 8px;
    border: none;
    border-bottom: 1px solid #dcdde1;
    font-weight: bold;
    color: #2c3e50;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
}

QScrollBar:vertical {
    border: none;
    background: #f0f0f0;
    width: 10px;
    border-radius: 5px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background: #c0c0c0;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background: #a0a0a0;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background: #f0f0f0;
    height: 10px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background: #c0c0c0;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal:hover {
    background: #a0a0a0;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM2MDYwNjAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cG9seWxpbmUgcG9pbnRzPSI2IDkgMTIgMTUgMTggOSI+PC9wb2x5bGluZT48L3N2Zz4=);
}

QSpinBox::up-button, QSpinBox::down-button {
    border: none;
    background: transparent;
    width: 20px;
}

QSpinBox::up-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM2MDYwNjAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cG9seWxpbmUgcG9pbnRzPSIxOCAxNSAxMiA5IDYgMTUiPjwvcG9seWxpbmU+PC9zdmc+);
}

QSpinBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM2MDYwNjAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cG9seWxpbmUgcG9pbnRzPSI2IDkgMTIgMTUgMTggOSI+PC9wb2x5bGluZT48L3N2Zz4=);
}
"""

class VideoConverterThread(QThread):
    progress_update = pyqtSignal(float, float, str)
    file_added = pyqtSignal(str)  # 新增文件添加信号
    file_progress_update = pyqtSignal(str, float)  # 新增单个文件进度更新信号
    conversion_complete = pyqtSignal()
    error_occurred = pyqtSignal(str)  # 新增错误信号
    conversion_started = pyqtSignal()  # 新增开始信号

    def __init__(self, config):
        super().__init__()
        self.config = config
        self.paused = False
        self.start_time = None
        self.pause_event = None
        self.pause_start_time = None
        self.current_files = []  # 记录当前处理的文件
        
        # 优化进度更新机制
        self.last_progress_update = 0
        self.progress_update_interval = 0.1  # 100ms更新间隔，提高界面流畅度
        import threading
        self.progress_lock = threading.Lock()

    def run(self):
        try:
            print("Starting video conversion...")
            self.start_time = time.time()
            from threading import Event
            self.pause_event = Event()
            
            # 检查输入目录是否存在
            input_path = Path(self.config['input_dir'])
            if not input_path.exists() or not input_path.is_dir():
                raise RuntimeError(f"输入目录 '{self.config['input_dir']}' 不存在或不是一个文件夹")
            
            # 检查输出目录是否可写
            output_path = Path(self.config['output_dir'])
            try:
                output_path.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                raise RuntimeError(f"无法创建输出目录 '{self.config['output_dir']}': {str(e)}")
            
            # 检查依赖
            script.check_dependencies()
            
            # 发出开始信号
            self.conversion_started.emit()
            
            # 获取视频文件列表
            video_files = [f for f in input_path.iterdir() if f.suffix.lower() in self.config['supported_formats']]
            video_files = [f for f in video_files if self.config['min_size'] <= f.stat().st_size / (1024 * 1024) <= self.config['max_size']]
            
            # 发送文件添加信号
            for file in video_files:
                self.file_added.emit(file.name)
                self.current_files.append(file.name)
            
            # 开始转换
            script.transcode_videos_with_detailed_progress(self.config, self.update_progress, self.pause_event)
            self.conversion_complete.emit()
        except Exception as e:
            print(f"转换过程发生错误: {e}")
            self.error_occurred.emit(str(e))
        finally:
            # 确保资源清理
            self._cleanup_resources()
    
    def _cleanup_resources(self):
        """清理线程资源"""
        try:
            # 清理暂停事件
            if hasattr(self, 'pause_event') and self.pause_event:
                self.pause_event.clear()
            
            # 重置状态
            self.paused = False
            self.pause_start_time = None
            
            # 强制垃圾回收
            import gc
            gc.collect()
        except Exception as e:
            print(f"资源清理时出错: {e}")

    def update_progress(self, total_progress, file_progress, current_file):
        """优化的进度回调函数，提高进度条流畅度"""
        import time
        current_time = time.time()
        
        with self.progress_lock:
            # 对于重要进度节点立即更新，或达到时间间隔时更新
            important_progress = file_progress in [0.0, 0.25, 0.5, 0.75, 1.0] or abs(file_progress - 1.0) < 0.01
            time_elapsed = current_time - self.last_progress_update >= self.progress_update_interval
            
            if important_progress or time_elapsed:
                self.progress_update.emit(total_progress, file_progress, current_file)
                
                # 更新单个文件进度
                if current_file:
                    file_name = Path(current_file).name
                    self.file_progress_update.emit(file_name, file_progress)
                
                self.last_progress_update = current_time

    def pause(self):
        """优化的暂停转换机制"""
        if not self.paused:  # 避免重复暂停
            self.paused = True
            if self.pause_event:
                self.pause_event.set()
                self.pause_start_time = time.time()

    def resume(self):
        """优化的恢复转换机制"""
        if self.paused:  # 只有在暂停状态下才恢复
            self.paused = False
            if self.pause_event:
                self.pause_event.clear()
                if self.pause_start_time:
                    self.start_time += time.time() - self.pause_start_time
                    self.pause_start_time = None

    def elapsed_time(self):
        if self.start_time is None:
            return 0
        return time.time() - self.start_time

class StyledFrame(QFrame):
    def __init__(self):
        super().__init__()
        self.setFrameShape(QFrame.StyledPanel)
        self.setStyleSheet("""
            StyledFrame {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }
        """)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频批量转换工具")
        self.setWindowIcon(QIcon("icon.png"))
        self.setMinimumSize(800, 700)  # 设置最小窗口大小
        self.resize(1000, 950)  # 设置默认窗口大小，进一步增加高度和宽度以提供更好的显示效果
        
        # 应用全局样式表
        QApplication.instance().setStyleSheet(GLOBAL_STYLE)
        
        # 加载配置
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.config = {
                'input_dir': './input',
                'output_dir': './output',
                'video_format': 'mp4',
                'resolution': '1280x720',
                'video_bitrate': '3000k',
                'audio_bitrate': '128k',
                'overwrite': True,
                'use_gpu': True,
                'max_retries': 1,
                'supported_formats': ['.wmv', '.avi', '.mp4', '.mkv'],
                'output_name': '{name}_{resolution}_{timestamp}',
                'min_size': 0,
                'max_size': 10240,
                'priority': 32,
                'remember_dirs': False,
                'keep_original_resolution': False
            }
        
        # 创建日志文本框（不显示在界面上，但用于记录日志）
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.hide()
        
        self.init_ui()

    def init_ui(self):
        print("Initializing UI...")
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout()
        main_layout.setSpacing(8)  # 减小间距
        main_layout.setContentsMargins(12, 12, 12, 12)  # 减小边距

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                background-color: #ffffff;
                padding: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            }
            QTabBar::tab {
                background: transparent;
                border: none;
                border-bottom: 2px solid transparent;
                padding: 12px 30px;
                margin-right: 6px;
                font-size: 14px;
                min-width: 120px;
                color: #636e72;
            }
            QTabBar::tab:selected {
                border-bottom: 2px solid #00b894;
                color: #00b894;
                font-weight: bold;
            }
            QTabBar::tab:hover:!selected {
                color: #2c3e50;
                border-bottom: 2px solid #dcdde1;
            }
        """)
        
        # 移除标签页图标
        self.tab_widget.setTabIcon(0, QIcon())
        self.tab_widget.setTabIcon(1, QIcon())
        self.tab_widget.setTabIcon(2, QIcon())

        # 创建设置标签页
        settings_tab = QWidget()
        settings_layout = QVBoxLayout(settings_tab)
        settings_layout.setSpacing(12)
        settings_layout.setContentsMargins(15, 15, 15, 15)

        # 基本设置框架
        basic_frame = StyledFrame()
        basic_layout = QVBoxLayout(basic_frame)
        basic_layout.setSpacing(10)
        basic_layout.setContentsMargins(15, 15, 15, 15)

        # 输入目录
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("输入目录:"))
        self.input_dir = QLineEdit(self.config.get('remembered_input_dir', self.config['input_dir']))
        self.input_dir.setPlaceholderText("选择输入视频所在目录")
        input_layout.addWidget(self.input_dir)
        input_btn = QPushButton("浏览")
        input_btn.setToolTip("选择包含需要转换视频的文件夹")
        input_btn.setStyleSheet("""
            QPushButton {
                background-color: #0984e3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #0652DD;
            }
            QPushButton:pressed {
                background-color: #0341b0;
            }
        """)
        input_btn.clicked.connect(self.select_input_dir)
        input_layout.addWidget(input_btn)
        basic_layout.addLayout(input_layout)

        # 输出目录
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        self.output_dir = QLineEdit(self.config.get('remembered_output_dir', self.config['output_dir']))
        self.output_dir.setPlaceholderText("选择转换后视频的保存目录")
        output_layout.addWidget(self.output_dir)
        output_btn = QPushButton("浏览")
        output_btn.setToolTip("选择转换后视频的保存位置")
        output_btn.setStyleSheet("""
            QPushButton {
                background-color: #0984e3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #0652DD;
            }
            QPushButton:pressed {
                background-color: #0341b0;
            }
        """)
        output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(output_btn)
        basic_layout.addLayout(output_layout)

        # 记住文件夹选择
        self.remember_dirs = QCheckBox("记住文件夹选择")
        self.remember_dirs.setToolTip("下次启动时自动填充上次使用的目录")
        self.remember_dirs.setChecked(self.config.get('remember_dirs', False))
        basic_layout.addWidget(self.remember_dirs)

        settings_layout.addWidget(basic_frame)

        # 创建水平布局来并排显示转换设置和高级设置
        settings_horizontal_layout = QHBoxLayout()
        settings_horizontal_layout.setSpacing(15)
        settings_layout.addWidget(basic_frame)
        settings_layout.addSpacing(15)

        # 转换设置框架
        convert_frame = StyledFrame()
        convert_layout = QVBoxLayout(convert_frame)
        convert_layout.setSpacing(10)
        convert_layout.setContentsMargins(15, 15, 15, 15)

        # 保持原分辨率选项
        self.keep_original_resolution = QCheckBox("保持原视频分辨率")
        self.keep_original_resolution.setToolTip("保持原视频分辨率不变，仅转换格式")
        self.keep_original_resolution.setChecked(self.config.get('keep_original_resolution', False))
        self.keep_original_resolution.stateChanged.connect(self.toggle_resolution_settings)
        convert_layout.addWidget(self.keep_original_resolution)

        # 视频格式
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("输出格式:"))
        self.video_format = QComboBox()
        self.video_format.addItems(['mp4', 'mkv', 'avi', 'mov', 'webm', 'flv'])
        self.video_format.setCurrentText(self.config['video_format'])
        self.video_format.setToolTip("选择输出视频的格式")
        format_layout.addWidget(self.video_format)
        format_layout.addStretch()
        convert_layout.addLayout(format_layout)

        # 分辨率
        resolution_layout = QHBoxLayout()
        resolution_layout.addWidget(QLabel("分辨率:"))
        self.resolution = QComboBox()
        self.resolution.addItems(['1920x1080', '1280x720', '854x480', '640x360'])
        self.resolution.setCurrentText(self.config['resolution'])
        self.resolution.setToolTip("选择输出视频的分辨率")
        resolution_layout.addWidget(self.resolution)
        resolution_layout.addStretch()
        convert_layout.addLayout(resolution_layout)

        # 视频比特率
        video_bitrate_layout = QHBoxLayout()
        video_bitrate_layout.addWidget(QLabel("视频比特率:"))
        self.video_bitrate = QLineEdit(self.config['video_bitrate'])
        self.video_bitrate.setToolTip("设置视频的比特率，例如：2000k")
        video_bitrate_layout.addWidget(self.video_bitrate)
        video_bitrate_layout.addStretch()
        convert_layout.addLayout(video_bitrate_layout)

        # 音频比特率
        audio_bitrate_layout = QHBoxLayout()
        audio_bitrate_layout.addWidget(QLabel("音频比特率:"))
        self.audio_bitrate = QLineEdit(self.config['audio_bitrate'])
        self.audio_bitrate.setToolTip("设置音频的比特率，例如：128k")
        audio_bitrate_layout.addWidget(self.audio_bitrate)
        audio_bitrate_layout.addStretch()
        convert_layout.addLayout(audio_bitrate_layout)

        # 初始化分辨率设置状态
        self.toggle_resolution_settings()

        # 高级设置框架
        advanced_frame = StyledFrame()
        advanced_layout = QVBoxLayout(advanced_frame)
        advanced_layout.setSpacing(10)
        advanced_layout.setContentsMargins(15, 15, 15, 15)

        # 覆盖文件选项
        self.overwrite = QCheckBox("覆盖已存在的文件")
        self.overwrite.setToolTip("如果输出目录已存在同名文件，是否覆盖")
        self.overwrite.setChecked(self.config['overwrite'])
        advanced_layout.addWidget(self.overwrite)
        
        # 自动检测所有视频文件选项
        self.auto_detect_videos = QCheckBox("自动检测所有视频文件")
        self.auto_detect_videos.setToolTip("使用ffprobe自动检测所有可能的视频文件，即使扩展名不在支持列表中")
        self.auto_detect_videos.setChecked(self.config.get('auto_detect_videos', False))
        advanced_layout.addWidget(self.auto_detect_videos)

        # 使用GPU选项
        self.use_gpu = QCheckBox("使用GPU加速")
        self.use_gpu.setToolTip("启用GPU加速可以提高转换速度（需要支持的显卡）")
        self.use_gpu.setChecked(self.config['use_gpu'])
        advanced_layout.addWidget(self.use_gpu)

        # 最大重试次数
        retry_layout = QHBoxLayout()
        retry_layout.addWidget(QLabel("最大重试次数:"))
        self.max_retries = QSpinBox()
        self.max_retries.setRange(0, 10)
        self.max_retries.setValue(self.config['max_retries'])
        self.max_retries.setToolTip("转换失败时的最大重试次数")
        retry_layout.addWidget(self.max_retries)
        retry_layout.addStretch()
        advanced_layout.addLayout(retry_layout)

        # 将转换设置和高级设置添加到水平布局中
        settings_horizontal_layout.addWidget(convert_frame, 1)
        settings_horizontal_layout.addWidget(advanced_frame, 1)

        # 将设置布局添加到主布局
        settings_layout.addLayout(settings_horizontal_layout)
        settings_layout.addSpacing(15)

        # 控制按钮区域
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(0, 4, 0, 4)
        
        # 开始转换按钮
        self.start_btn = QPushButton("开始转换")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #00b894;
                color: white;
                font-weight: bold;
                min-width: 140px;
                min-height: 45px;
                border-radius: 8px;
                font-size: 15px;
                box-shadow: 0 2px 6px rgba(0, 184, 148, 0.3);
            }
            QPushButton:hover {
                background-color: #00a885;
            }
            QPushButton:pressed {
                background-color: #009874;
                box-shadow: 0 1px 3px rgba(0, 184, 148, 0.3);
            }
        """)
        self.start_btn.clicked.connect(self.start_conversion)
        control_layout.addWidget(self.start_btn)

        # 暂停/继续按钮
        self.pause_resume_btn = QPushButton("暂停")
        self.pause_resume_btn.setStyleSheet("""
            QPushButton {
                background-color: #fdcb6e;
                color: #2d3436;
                font-weight: bold;
                min-width: 140px;
                min-height: 45px;
                border-radius: 8px;
                font-size: 15px;
                box-shadow: 0 2px 6px rgba(253, 203, 110, 0.3);
            }
            QPushButton:hover {
                background-color: #fdb54d;
            }
            QPushButton:pressed {
                background-color: #f0a04b;
                box-shadow: 0 1px 3px rgba(253, 203, 110, 0.3);
            }
            QPushButton:disabled {
                background-color: #d1d1d1;
                color: #7f8c8d;
                box-shadow: none;
            }
        """)
        self.pause_resume_btn.clicked.connect(self.toggle_pause_resume)
        self.pause_resume_btn.setEnabled(False)
        control_layout.addWidget(self.pause_resume_btn)

        settings_layout.addLayout(control_layout)

        # 将设置标签页添加到标签页控件
        self.tab_widget.addTab(settings_tab, "设置")

        # 创建转换进度标签页
        progress_tab = QWidget()
        progress_tab_layout = QVBoxLayout(progress_tab)
        progress_tab_layout.setSpacing(15)
        progress_tab_layout.setContentsMargins(15, 15, 15, 15)

        # 进度显示区域框架
        progress_frame = StyledFrame()
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setSpacing(12)
        progress_layout.setContentsMargins(15, 15, 15, 15)

        # 总体进度条
        total_progress_label = QLabel("总体进度:")
        total_progress_label.setStyleSheet("font-weight: 600; margin-top: 5px; font-size: 14px; color: #2c3e50;")
        progress_layout.addWidget(total_progress_label)
        self.total_progress_bar = QProgressBar()
        self.total_progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 8px;
                text-align: center;
                background-color: #f0f0f0;
                min-height: 26px;
                font-weight: bold;
                font-size: 13px;
                color: #2c3e50;
            }
            QProgressBar::chunk {
                background-color: #00b894;
                border-radius: 8px;
            }
        """)
        progress_layout.addWidget(self.total_progress_bar)

        # 当前文件进度条
        file_progress_label = QLabel("当前文件进度:")
        file_progress_label.setStyleSheet("font-weight: 600; margin-top: 10px; font-size: 14px; color: #2c3e50;")
        progress_layout.addWidget(file_progress_label)
        self.file_progress_bar = QProgressBar()
        self.file_progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 8px;
                text-align: center;
                background-color: #f0f0f0;
                min-height: 26px;
                font-weight: bold;
                font-size: 13px;
                color: #2c3e50;
            }
            QProgressBar::chunk {
                background-color: #0984e3;
                border-radius: 8px;
            }
        """)
        progress_layout.addWidget(self.file_progress_bar)

        # 当前处理文件名
        self.current_file_label = QLabel("当前处理文件: ")
        self.current_file_label.setStyleSheet("margin-top: 15px; padding: 10px; background-color: #f5f6fa; border-radius: 6px; font-size: 13px;")
        progress_layout.addWidget(self.current_file_label)

        # 状态信息
        self.status_label = QLabel("状态: 就绪")
        self.status_label.setStyleSheet("font-weight: bold; color: #2d3436; margin-top: 10px; padding: 10px; background-color: #f5f6fa; border-radius: 6px; font-size: 13px;")
        progress_layout.addWidget(self.status_label)

        # 处理速度和预估时间
        speed_eta_layout = QHBoxLayout()
        
        # 处理速度
        self.speed_label = QLabel("处理速度: 0.0x")
        self.speed_label.setStyleSheet("font-weight: bold; color: #2d3436; padding: 8px; background-color: #f5f6fa; border-radius: 6px; font-size: 13px;")
        speed_eta_layout.addWidget(self.speed_label)
        
        # 预估剩余时间
        self.eta_label = QLabel("剩余时间: 00:00:00")
        self.eta_label.setStyleSheet("font-weight: bold; color: #2d3436; padding: 8px; background-color: #f5f6fa; border-radius: 6px; font-size: 13px;")
        speed_eta_layout.addWidget(self.eta_label)
        
        progress_layout.addLayout(speed_eta_layout)

        progress_tab_layout.addWidget(progress_frame)
        progress_tab_layout.addSpacing(15)

        # 视频转换进度表格
        table_frame = StyledFrame()
        table_layout = QVBoxLayout(table_frame)
        table_layout.setSpacing(10)
        table_layout.setContentsMargins(15, 15, 15, 15)

        # 表格标题
        table_title = QLabel("视频转换列表")
        table_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2d3436; margin-bottom: 15px;")
        table_layout.addWidget(table_title)

        # 创建表格
        self.files_table = QTableWidget()
        self.files_table.setColumnCount(3)
        self.files_table.setHorizontalHeaderLabels(["文件名", "进度", "状态"])
        self.files_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.files_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.files_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.files_table.setStyleSheet("""
            QTableWidget {
                border: none;
                background-color: white;
                gridline-color: #f0f0f0;
                border-radius: 8px;
            }
            QHeaderView::section {
                background-color: #f5f6fa;
                padding: 10px;
                border: none;
                border-bottom: 1px solid #dcdde1;
                font-weight: bold;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f0f0f0;
                font-size: 13px;
            }
        """)
        self.files_table.setMinimumHeight(250)
        table_layout.addWidget(self.files_table)

        progress_tab_layout.addWidget(table_frame)

        # 将转换进度标签页添加到标签页控件
        self.tab_widget.addTab(progress_tab, "转换进度")

        # 创建关于标签页
        about_tab = QWidget()
        about_layout = QVBoxLayout(about_tab)
        about_layout.setSpacing(10)
        about_layout.setContentsMargins(20, 20, 20, 20)

        # 创建滚动区域，使内容可滚动
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setStyleSheet("background: transparent;")
        
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(20)
        scroll_layout.setContentsMargins(10, 10, 10, 10)
        
        # 关于信息框架
        about_frame = StyledFrame()
        about_frame_layout = QVBoxLayout(about_frame)
        about_frame_layout.setSpacing(20)
        about_frame_layout.setContentsMargins(25, 25, 25, 25)
        about_frame_layout.setAlignment(Qt.AlignCenter)

        # 软件标题
        software_title = QLabel("视频批量转换工具")
        software_title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2d3436; margin-bottom: 5px;")
        software_title.setAlignment(Qt.AlignCenter)
        about_frame_layout.addWidget(software_title)

        # 软件图标
        icon_label = QLabel()
        icon_pixmap = QPixmap("icon.png").scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        icon_label.setPixmap(icon_pixmap)
        icon_label.setAlignment(Qt.AlignCenter)
        about_frame_layout.addWidget(icon_label)

        # 软件描述
        description = QLabel("这是一个功能强大的视频批量转换工具，支持几乎所有ffmpeg能处理的视频格式，可以帮助您将多个视频文件转换为指定格式和分辨率，并具有智能码率调整功能，能够根据视频内容和格式自动优化视频质量。")
        description.setStyleSheet("font-size: 14px; color: #2d3436; line-height: 1.6; margin: 0 20px;")
        description.setWordWrap(True)
        description.setAlignment(Qt.AlignCenter)
        about_frame_layout.addWidget(description)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #e0e0e0; max-height: 1px; margin: 10px 0;")
        about_frame_layout.addWidget(separator)

        # 功能特点
        features_label = QLabel("主要功能:")
        features_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2d3436;")
        features_label.setAlignment(Qt.AlignCenter)
        about_frame_layout.addWidget(features_label)

        # 功能列表
        features_frame = QFrame()
        features_frame.setStyleSheet("background-color: #f8f9fa; border-radius: 10px; padding: 15px;")
        features_layout = QVBoxLayout(features_frame)
        
        features = QLabel(
            "• 支持几乎所有ffmpeg能处理的视频格式，包括mp4、mkv、avi、mov、flv等\n"
            "• 自动检测视频文件功能，即使扩展名不在支持列表中也能处理\n"
            "• 智能码率调整，根据视频内容和格式自动优化质量\n"
            "• 可自定义分辨率和比特率，满足不同需求\n"
            "• 支持GPU加速，大幅提高转换速度\n"
            "• 保持原分辨率选项，保证视频原始质量\n"
            "• 实时显示转换进度和详细日志\n"
            "• 可暂停和继续转换过程，灵活控制"
        )
        features.setStyleSheet("font-size: 14px; color: #2d3436; line-height: 2.0;")
        features.setAlignment(Qt.AlignLeft)
        features_layout.addWidget(features)
        
        about_frame_layout.addWidget(features_frame)

        # 分隔线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        separator2.setStyleSheet("background-color: #e0e0e0; max-height: 1px; margin: 10px 0;")
        about_frame_layout.addWidget(separator2)

        # 版权信息
        copyright_label = QLabel("© 2024 视频批量转换工具")
        copyright_label.setStyleSheet("font-size: 12px; color: #7f8c8d;")
        copyright_label.setAlignment(Qt.AlignCenter)
        about_frame_layout.addWidget(copyright_label)

        scroll_layout.addWidget(about_frame)
        scroll_area.setWidget(scroll_content)
        about_layout.addWidget(scroll_area)

        # 创建性能监控标签页
        performance_tab = QWidget()
        performance_layout = QVBoxLayout(performance_tab)
        performance_layout.setSpacing(15)
        performance_layout.setContentsMargins(15, 15, 15, 15)

        # 性能监控标题
        perf_title = QLabel("实时性能监控")
        perf_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2d3436; margin-bottom: 10px;")
        perf_title.setAlignment(Qt.AlignCenter)
        performance_layout.addWidget(perf_title)

        # 性能指标显示区域
        metrics_frame = StyledFrame()
        metrics_layout = QVBoxLayout(metrics_frame)
        metrics_layout.setSpacing(15)
        metrics_layout.setContentsMargins(20, 20, 20, 20)

        # CPU使用率
        cpu_layout = QHBoxLayout()
        cpu_label = QLabel("CPU使用率:")
        cpu_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; min-width: 100px;")
        self.cpu_value_label = QLabel("0.0%")
        self.cpu_value_label.setStyleSheet("font-size: 14px; color: #e17055; font-weight: bold;")
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setRange(0, 100)
        self.cpu_progress.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 8px;
                text-align: center;
                background-color: #f0f0f0;
                min-height: 20px;
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
            }
            QProgressBar::chunk {
                background-color: #e17055;
                border-radius: 8px;
            }
        """)
        cpu_layout.addWidget(cpu_label)
        cpu_layout.addWidget(self.cpu_value_label)
        cpu_layout.addWidget(self.cpu_progress, 2)
        metrics_layout.addLayout(cpu_layout)

        # 内存使用率
        memory_layout = QHBoxLayout()
        memory_label = QLabel("内存使用率:")
        memory_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; min-width: 100px;")
        self.memory_value_label = QLabel("0.0%")
        self.memory_value_label.setStyleSheet("font-size: 14px; color: #0984e3; font-weight: bold;")
        self.memory_progress = QProgressBar()
        self.memory_progress.setRange(0, 100)
        self.memory_progress.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 8px;
                text-align: center;
                background-color: #f0f0f0;
                min-height: 20px;
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
            }
            QProgressBar::chunk {
                background-color: #0984e3;
                border-radius: 8px;
            }
        """)
        memory_layout.addWidget(memory_label)
        memory_layout.addWidget(self.memory_value_label)
        memory_layout.addWidget(self.memory_progress, 2)
        metrics_layout.addLayout(memory_layout)

        # 磁盘I/O
        disk_layout = QVBoxLayout()
        disk_title = QLabel("磁盘I/O:")
        disk_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; margin-top: 10px;")
        disk_layout.addWidget(disk_title)
        
        disk_info_layout = QHBoxLayout()
        self.disk_read_label = QLabel("读取: 0 MB/s")
        self.disk_read_label.setStyleSheet("font-size: 13px; color: #00b894; padding: 5px; background-color: #f5f6fa; border-radius: 4px;")
        self.disk_write_label = QLabel("写入: 0 MB/s")
        self.disk_write_label.setStyleSheet("font-size: 13px; color: #fdcb6e; padding: 5px; background-color: #f5f6fa; border-radius: 4px;")
        disk_info_layout.addWidget(self.disk_read_label)
        disk_info_layout.addWidget(self.disk_write_label)
        disk_layout.addLayout(disk_info_layout)
        metrics_layout.addLayout(disk_layout)

        # 峰值统计
        peak_layout = QVBoxLayout()
        peak_title = QLabel("峰值统计:")
        peak_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; margin-top: 10px;")
        peak_layout.addWidget(peak_title)
        
        peak_info_layout = QHBoxLayout()
        self.peak_cpu_label = QLabel("峰值CPU: 0.0%")
        self.peak_cpu_label.setStyleSheet("font-size: 13px; color: #e17055; padding: 5px; background-color: #f5f6fa; border-radius: 4px;")
        self.peak_memory_label = QLabel("峰值内存: 0.0%")
        self.peak_memory_label.setStyleSheet("font-size: 13px; color: #0984e3; padding: 5px; background-color: #f5f6fa; border-radius: 4px;")
        peak_info_layout.addWidget(self.peak_cpu_label)
        peak_info_layout.addWidget(self.peak_memory_label)
        peak_layout.addLayout(peak_info_layout)
        metrics_layout.addLayout(peak_layout)

        # 监控状态
        self.monitoring_status_label = QLabel("监控状态: 未启动")
        self.monitoring_status_label.setStyleSheet("font-weight: bold; color: #636e72; margin-top: 15px; padding: 10px; background-color: #f5f6fa; border-radius: 6px; font-size: 13px; text-align: center;")
        self.monitoring_status_label.setAlignment(Qt.AlignCenter)
        metrics_layout.addWidget(self.monitoring_status_label)

        performance_layout.addWidget(metrics_frame)
        
        # 添加弹性空间
        performance_layout.addStretch()

        # 将性能监控标签页添加到标签页控件
        self.tab_widget.addTab(performance_tab, "性能监控")

        # 将关于标签页添加到标签页控件
        self.tab_widget.addTab(about_tab, "关于")

        # 将标签页控件添加到主布局
        main_layout.addWidget(self.tab_widget)

        central_widget.setLayout(main_layout)

        self.processed_files = 0
        self.total_files = 0

        # 初始化性能监控定时器
        if PERFORMANCE_MONITORING_AVAILABLE:
            self.performance_timer = QTimer()
            self.performance_timer.timeout.connect(self.update_performance_display)
            self.performance_timer.start(1000)  # 每秒更新一次
            self.monitoring_status_label.setText("监控状态: 运行中")
            self.monitoring_status_label.setStyleSheet("font-weight: bold; color: #00b894; margin-top: 15px; padding: 10px; background-color: #f5f6fa; border-radius: 6px; font-size: 13px; text-align: center;")
        else:
            self.monitoring_status_label.setText("监控状态: 不可用")
            self.monitoring_status_label.setStyleSheet("font-weight: bold; color: #e17055; margin-top: 15px; padding: 10px; background-color: #f5f6fa; border-radius: 6px; font-size: 13px; text-align: center;")

        # 注册退出时的清理函数
        atexit.register(self.cleanup_on_exit)
    
    def update_performance_display(self):
        """更新性能监控显示"""
        if not PERFORMANCE_MONITORING_AVAILABLE:
            return
            
        try:
            stats = performance_monitor.get_current_stats()
            if stats:
                # 更新CPU使用率
                cpu_percent = stats.get('current_cpu', 0)
                self.cpu_value_label.setText(f"{cpu_percent:.1f}%")
                self.cpu_progress.setValue(int(cpu_percent))
                
                # 更新内存使用率
                memory_percent = stats.get('current_memory', 0)
                self.memory_value_label.setText(f"{memory_percent:.1f}%")
                self.memory_progress.setValue(int(memory_percent))
                
                # 更新磁盘I/O
                disk_read = stats.get('disk_read_speed', 0) / (1024 * 1024)  # 转换为MB/s
                disk_write = stats.get('disk_write_speed', 0) / (1024 * 1024)  # 转换为MB/s
                self.disk_read_label.setText(f"读取: {disk_read:.1f} MB/s")
                self.disk_write_label.setText(f"写入: {disk_write:.1f} MB/s")
                
                # 更新峰值统计
                peak_cpu = stats.get('peak_cpu', 0)
                peak_memory = stats.get('peak_memory', 0)
                self.peak_cpu_label.setText(f"峰值CPU: {peak_cpu:.1f}%")
                self.peak_memory_label.setText(f"峰值内存: {peak_memory:.1f}%")
                
        except Exception as e:
            print(f"更新性能显示时出错: {e}")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 更新配置
            self.config['input_dir'] = self.input_dir.text()
            self.config['output_dir'] = self.output_dir.text()
            self.config['video_format'] = self.video_format.currentText()
            self.config['resolution'] = self.resolution.currentText()
            self.config['video_bitrate'] = self.video_bitrate.text()
            self.config['audio_bitrate'] = self.audio_bitrate.text()
            self.config['overwrite'] = self.overwrite.isChecked()
            self.config['use_gpu'] = self.use_gpu.isChecked()
            self.config['max_retries'] = self.max_retries.value()
            self.config['keep_original_resolution'] = self.keep_original_resolution.isChecked()
            self.config['auto_detect_videos'] = self.auto_detect_videos.isChecked()
            
            # 保存记住的目录选择
            if self.remember_dirs.isChecked():
                self.config['remembered_input_dir'] = self.input_dir.text()
                self.config['remembered_output_dir'] = self.output_dir.text()
            else:
                self.config.pop('remembered_input_dir', None)
                self.config.pop('remembered_output_dir', None)
            self.config['remember_dirs'] = self.remember_dirs.isChecked()
            
            # 保存到文件
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存配置时出错: {e}")

    def cleanup_on_exit(self):
        """在程序退出时清理资源"""
        if hasattr(self, 'converter_thread') and self.converter_thread is not None:
            if self.converter_thread.isRunning():
                if self.converter_thread.paused:
                    self.converter_thread.resume()  # 先恢复暂停的进程
                self.converter_thread.terminate()  # 终止转换线程
                self.converter_thread.wait()  # 等待线程结束
        
        # 停止性能监控
        if PERFORMANCE_MONITORING_AVAILABLE:
            try:
                performance_monitor.stop_monitoring()
            except Exception as e:
                print(f"停止性能监控时出错: {e}")
        
        script.cleanup_processes()  # 清理所有ffmpeg进程

    def closeEvent(self, event):
        """优化的窗口关闭事件，包含完整的资源清理"""
        if hasattr(self, 'converter_thread') and self.converter_thread and self.converter_thread.isRunning():
            reply = QMessageBox.question(self, '确认退出', 
                                       '转换正在进行中，确定要退出吗？',
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                self._cleanup_and_exit()
                event.accept()
            else:
                event.ignore()
        else:
            self._cleanup_and_exit()
            event.accept()
    
    def _cleanup_and_exit(self):
        """清理所有资源并退出"""
        try:
            # 停止转换线程
            if hasattr(self, 'converter_thread') and self.converter_thread:
                if self.converter_thread.isRunning():
                    self.converter_thread.terminate()
                    self.converter_thread.wait(3000)  # 等待最多3秒
                
                # 清理线程资源
                self.converter_thread._cleanup_resources()
                self.converter_thread = None
            
            # 清理脚本中的全局资源
            script.cleanup_processes()
            
            # 保存配置
            self.save_config()
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
        except Exception as e:
            print(f"退出清理时出错: {e}")

    def toggle_resolution_settings(self):
        """根据是否保持原分辨率来启用/禁用相关设置"""
        keep_original = self.keep_original_resolution.isChecked()
        
        # 禁用分辨率、视频比特率和音频比特率
        self.resolution.setEnabled(not keep_original)
        self.video_bitrate.setEnabled(not keep_original)
        self.audio_bitrate.setEnabled(not keep_original)
        
        # 设置控件的样式以表示禁用状态
        if keep_original:
            self.resolution.setStyleSheet("background-color: #f0f0f0; color: #a0a0a0;")
            self.video_bitrate.setStyleSheet("background-color: #f0f0f0; color: #a0a0a0;")
            self.audio_bitrate.setStyleSheet("background-color: #f0f0f0; color: #a0a0a0;")
        else:
            self.resolution.setStyleSheet("")
            self.video_bitrate.setStyleSheet("")
            self.audio_bitrate.setStyleSheet("")

    def select_input_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择输入目录")
        if dir_path:
            self.input_dir.setText(dir_path)

    def select_output_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir.setText(dir_path)

    def start_conversion(self):
        print("Starting conversion process...")
        # 更新配置
        self.config['input_dir'] = self.input_dir.text()
        self.config['output_dir'] = self.output_dir.text()
        self.config['video_format'] = self.video_format.currentText()
        self.config['resolution'] = self.resolution.currentText()
        self.config['video_bitrate'] = self.video_bitrate.text()
        self.config['audio_bitrate'] = self.audio_bitrate.text()
        self.config['overwrite'] = self.overwrite.isChecked()
        self.config['use_gpu'] = self.use_gpu.isChecked()
        self.config['max_retries'] = self.max_retries.value()
        
        # 新增保持原分辨率的配置
        self.config['keep_original_resolution'] = self.keep_original_resolution.isChecked()
        
        # 新增自动检测视频文件的配置
        self.config['auto_detect_videos'] = self.auto_detect_videos.isChecked()

        # 检查输入目录是否存在
        input_path = Path(self.config['input_dir'])
        if not input_path.exists() or not input_path.is_dir():
            self.log_text.append(f"错误: 输入目录 '{self.config['input_dir']}' 不存在或不是一个文件夹")
            return

        # 获取实际的视频文件数量
        video_files = [f for f in input_path.iterdir() if f.suffix.lower() in self.config['supported_formats']]
        video_files = [f for f in video_files if self.config['min_size'] <= f.stat().st_size / (1024 * 1024) <= self.config['max_size']]
        
        if not video_files:
            self.log_text.append(f"错误: 输入目录中没有符合条件的视频文件")
            return
            
        self.total_files = len(video_files)
        self.processed_files = 0

        # 保存配置
        self.save_config()

        # 清空表格
        self.files_table.setRowCount(0)
            
        # 创建并启动转换线程
        self.converter_thread = VideoConverterThread(self.config)
        self.converter_thread.progress_update.connect(self.update_progress)
        self.converter_thread.conversion_complete.connect(self.conversion_finished)
        self.converter_thread.error_occurred.connect(self.handle_conversion_error)
        self.converter_thread.conversion_started.connect(self.conversion_started)
        self.converter_thread.file_added.connect(self.add_file_to_table)
        self.converter_thread.file_progress_update.connect(self.update_file_progress)
        self.converter_thread.start()

    def add_file_to_table(self, file_name):
        """添加文件到表格"""
        row_position = self.files_table.rowCount()
        self.files_table.insertRow(row_position)
        
        # 文件名
        file_item = QTableWidgetItem(file_name)
        self.files_table.setItem(row_position, 0, file_item)
        
        # 进度
        progress_item = QTableWidgetItem("0%")
        progress_item.setTextAlignment(Qt.AlignCenter)
        self.files_table.setItem(row_position, 1, progress_item)
        
        # 状态
        status_item = QTableWidgetItem("等待中")
        status_item.setTextAlignment(Qt.AlignCenter)
        self.files_table.setItem(row_position, 2, status_item)

    def update_file_progress(self, file_name, progress):
        """更新文件进度"""
        # 查找文件所在行
        for row in range(self.files_table.rowCount()):
            if self.files_table.item(row, 0).text() == file_name:
                # 更新进度
                progress_text = f"{int(progress * 100)}%"
                self.files_table.item(row, 1).setText(progress_text)
                
                # 更新状态
                if progress < 1.0:
                    status = "转换中"
                else:
                    status = "已完成"
                self.files_table.item(row, 2).setText(status)
                
                # 设置颜色
                if progress >= 1.0:
                    self.files_table.item(row, 1).setForeground(QColor("#00b894"))
                    self.files_table.item(row, 2).setForeground(QColor("#00b894"))
                else:
                    self.files_table.item(row, 1).setForeground(QColor("#0984e3"))
                    self.files_table.item(row, 2).setForeground(QColor("#0984e3"))
                break

    def update_progress(self, total_progress, file_progress, current_file):
        total_percent = int(total_progress * 100)
        file_percent = int(file_progress * 100)
        self.total_progress_bar.setValue(total_percent)
        self.file_progress_bar.setValue(file_percent)
        
        # 限制显示的文件名长度
        max_filename_length = 50
        if len(current_file) > max_filename_length:
            displayed_filename = current_file[:max_filename_length-3] + "..."
        else:
            displayed_filename = current_file
        
        self.current_file_label.setText(f"当前处理文件: {displayed_filename}")
        
        # 当文件进度为100%时增加已处理文件计数
        if file_percent == 100:
            self.processed_files += 1
        
        # 计算处理速度和预估时间（基于高精度数据）
        if total_progress > 0:
            elapsed_time = self.converter_thread.elapsed_time()
            estimated_total_time = elapsed_time / total_progress
            remaining_time = estimated_total_time - elapsed_time
            remaining_time_str = self.format_time(remaining_time)
            
            # 计算平均处理速度
            try:
                import script
                if hasattr(script, 'progress_manager') and script.progress_manager:
                    overall = script.progress_manager.get_overall_progress()
                    speed = overall.get('speed', 1.0)
                    eta = overall.get('eta', remaining_time)
                    
                    # 更新速度和ETA显示
                    self.speed_label.setText(f"处理速度: {speed:.1f}x")
                    self.eta_label.setText(f"剩余时间: {self.format_time(eta)}")
                else:
                    # 兼容模式
                    self.speed_label.setText(f"处理速度: {total_progress/elapsed_time:.1f}x")
                    self.eta_label.setText(f"剩余时间: {remaining_time_str}")
            except:
                # 回退到简单计算
                self.speed_label.setText(f"处理速度: {total_progress/elapsed_time:.1f}x")
                self.eta_label.setText(f"剩余时间: {remaining_time_str}")
        else:
            self.speed_label.setText("处理速度: 计算中...")
            self.eta_label.setText("剩余时间: 计算中...")

        # 根据文件进度显示详细的处理阶段
        if file_percent == 0:
            stage_text = "准备中 - 正在分析视频信息"
        elif file_percent < 5:
            stage_text = "初始化中 - 正在准备转码环境"
        elif file_percent < 10:
            stage_text = "分析中 - 正在读取视频参数"
        elif file_percent < 15:
            stage_text = "配置中 - 正在构建转码命令"
        elif file_percent < 20:
            stage_text = "启动中 - 正在启动FFmpeg进程"
        elif file_percent < 100:
            stage_text = f"转码中 - 进度 {file_percent}%"
        else:
            stage_text = "完成 - 文件处理完毕"
        
        status_text = f"状态: {stage_text} - 已处理 {self.processed_files}/{self.total_files} 个文件"
        self.status_label.setText(status_text)
        
        # 添加带时间戳的日志
        timestamp = time.strftime("%H:%M:%S", time.localtime())
        log_message = f"[{timestamp}] 总进度: {total_percent}%, 文件进度: {file_percent}%, 当前文件: {current_file}"
        self.log_text.append(log_message)
        self.log_text.moveCursor(QTextCursor.End)

    def conversion_finished(self):
        print("Conversion process completed.")
        self.start_btn.setEnabled(True)
        self.pause_resume_btn.setEnabled(False)
        self.total_progress_bar.setValue(100)
        self.file_progress_bar.setValue(100)
        self.current_file_label.setText("当前处理文件: 完成")
        self.status_label.setText("状态: 转换完成")
        self.speed_label.setText("处理速度: 完成")
        self.eta_label.setText("剩余时间: 00:00:00")
        
        # 停止性能监控
        if PERFORMANCE_MONITORING_AVAILABLE:
            performance_monitor.stop_monitoring()
            self.monitoring_status_label.setText("监控状态: 已完成")
            self.monitoring_status_label.setStyleSheet("font-weight: bold; color: #00b894; margin-top: 15px; padding: 10px; background-color: #f5f6fa; border-radius: 6px; font-size: 13px; text-align: center;")
        
        # 添加完成时间戳
        timestamp = time.strftime("%H:%M:%S", time.localtime())
        self.log_text.append(f"[{timestamp}] 转换过程完成。")

    def conversion_started(self):
        """转换开始时的处理"""
        self.start_btn.setEnabled(False)
        self.pause_resume_btn.setEnabled(True)
        self.status_label.setText("状态: 转换中")
        self.speed_label.setText("处理速度: 计算中...")
        self.eta_label.setText("剩余时间: 计算中...")
        self.log_text.append("开始转换过程...")
        
        # 重置进度条和计数
        self.total_progress_bar.setValue(0)
        self.file_progress_bar.setValue(0)
        self.processed_files = 0
        
        # 启动性能监控
        if PERFORMANCE_MONITORING_AVAILABLE:
            performance_monitor.start_monitoring()
            performance_monitor.reset_stats()
            self.monitoring_status_label.setText("监控状态: 转换中")
            self.monitoring_status_label.setStyleSheet("font-weight: bold; color: #fdcb6e; margin-top: 15px; padding: 10px; background-color: #f5f6fa; border-radius: 6px; font-size: 13px; text-align: center;")
        
        # 自动切换到转换进度标签页
        self.tab_widget.setCurrentIndex(1)  # 切换到第二个标签页（转换进度）

    def handle_conversion_error(self, error_message):
        """处理转换过程中的错误"""
        self.start_btn.setEnabled(True)
        self.pause_resume_btn.setEnabled(False)
        self.status_label.setText("状态: 错误")
        self.speed_label.setText("处理速度: 错误")
        self.eta_label.setText("剩余时间: 错误")
        
        # 记录错误信息到日志
        self.log_text.append(f"错误: {error_message}")
        
        # 显示错误消息
        QMessageBox.critical(self, "转换错误", f"转换过程中发生错误:\n{error_message}")
        
        # 重置进度条
        self.total_progress_bar.setValue(0)
        self.file_progress_bar.setValue(0)

    def toggle_pause_resume(self):
        if self.converter_thread and self.converter_thread.paused:
            self.converter_thread.resume()
            self.pause_resume_btn.setText("暂停")
            self.status_label.setText("状态: 转换中")
            self.log_text.append("恢复转换过程...")
        elif self.converter_thread:
            self.converter_thread.pause()
            self.pause_resume_btn.setText("继续")
            self.status_label.setText("状态: 已暂停")
            self.log_text.append("暂停转换过程...")

    @staticmethod
    def format_time(seconds):
        hours, remainder = divmod(int(seconds), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

if __name__ == '__main__':
    try:
        print("Creating QApplication...")
        app = QApplication(sys.argv)
        app.setStyle(QStyleFactory.create('Fusion'))  # 使用Fusion风格
        print("Creating MainWindow...")
        window = MainWindow()
        print("Showing MainWindow...")
        window.show()
        print("Entering event loop...")
        sys.exit(app.exec_())  # 恢复sys.exit()，但确保在其他地方不会提前退出
    except Exception as e:
        print(f"程序发生错误: {e}")
        sys.exit(1)
