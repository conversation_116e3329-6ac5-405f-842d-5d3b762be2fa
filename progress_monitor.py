#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度进度监控模块
提供实时、精确的转码进度跟踪和性能统计
"""

import re
import time
import threading
from collections import deque
from dataclasses import dataclass
from typing import Optional, Callable, Dict, Any
import psutil
import logging

@dataclass
class ProgressData:
    """进度数据结构"""
    file_name: str
    current_time: float
    total_duration: float
    progress: float  # 0.0-1.0
    speed: float  # 处理速度 (x)
    eta: float  # 预估剩余时间(秒)
    frame: int  # 当前帧数
    fps: float  # 帧率
    size: int  # 已处理大小(字节)
    bitrate: float  # 当前码率(kbps)
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class FFmpegProgressParser:
    """FFmpeg进度解析器 - 高精度版本"""
    
    def __init__(self, total_duration: float, file_name: str):
        self.total_duration = total_duration
        self.file_name = file_name
        self.start_time = time.time()
        self.last_update = 0
        
        # 解析模式
        self.time_pattern = re.compile(r'time=(\d{2}):(\d{2}):(\d{2}\.\d{2})')
        self.speed_pattern = re.compile(r'speed=(\d+\.?\d*)x')
        self.frame_pattern = re.compile(r'frame=(\d+)')
        self.fps_pattern = re.compile(r'fps=(\d+\.?\d*)')
        self.size_pattern = re.compile(r'size=\s*(\d+)(\w+)')
        self.bitrate_pattern = re.compile(r'bitrate=\s*(\d+\.?\d*)(\w+)')
        
        # 性能统计
        self.progress_history = deque(maxlen=100)
        self.speed_history = deque(maxlen=10)
        
    def parse_line(self, line: str) -> Optional[ProgressData]:
        """解析单行FFmpeg输出"""
        try:
            # 提取时间
            time_match = self.time_pattern.search(line)
            if not time_match:
                return None
                
            hours, minutes, seconds = time_match.groups()
            current_time = float(hours) * 3600 + float(minutes) * 60 + float(seconds)
            
            if current_time <= 0:
                return None
                
            # 计算进度
            progress = min(current_time / self.total_duration, 1.0)
            
            # 提取速度
            speed = 1.0
            speed_match = self.speed_pattern.search(line)
            if speed_match:
                speed = float(speed_match.group(1))
            
            # 提取帧信息
            frame = 0
            fps = 0.0
            frame_match = self.frame_pattern.search(line)
            if frame_match:
                frame = int(frame_match.group(1))
                
            fps_match = self.fps_pattern.search(line)
            if fps_match:
                fps = float(fps_match.group(1))
            
            # 提取大小和码率
            size = 0
            bitrate = 0.0
            
            size_match = self.size_pattern.search(line)
            if size_match:
                size_value, size_unit = size_match.groups()
                size = self._parse_size(size_value, size_unit)
            
            bitrate_match = self.bitrate_pattern.search(line)
            if bitrate_match:
                bitrate_value, bitrate_unit = bitrate_match.groups()
                bitrate = self._parse_bitrate(bitrate_value, bitrate_unit)
            
            # 计算预估时间
            elapsed = time.time() - self.start_time
            if speed > 0:
                eta = (self.total_duration - current_time) / speed
            else:
                eta = 0
            
            # 创建进度数据
            progress_data = ProgressData(
                file_name=self.file_name,
                current_time=current_time,
                total_duration=self.total_duration,
                progress=progress,
                speed=speed,
                eta=eta,
                frame=frame,
                fps=fps,
                size=size,
                bitrate=bitrate
            )
            
            # 记录历史
            self.progress_history.append(progress_data)
            self.speed_history.append(speed)
            
            return progress_data
            
        except Exception as e:
            logging.warning(f"解析FFmpeg进度失败: {e}")
            return None
    
    def _parse_size(self, value: str, unit: str) -> int:
        """解析文件大小"""
        multipliers = {'k': 1024, 'M': 1024**2, 'G': 1024**3}
        base = int(value)
        return base * multipliers.get(unit, 1)
    
    def _parse_bitrate(self, value: str, unit: str) -> float:
        """解析码率"""
        base = float(value)
        if unit == 'kbits/s':
            return base
        elif unit == 'bits/s':
            return base / 1000
        return base
    
    def get_average_speed(self) -> float:
        """获取平均处理速度"""
        if not self.speed_history:
            return 1.0
        return sum(self.speed_history) / len(self.speed_history)
    
    def get_smooth_eta(self) -> float:
        """获取平滑后的预估时间"""
        if len(self.progress_history) < 2:
            return 0
        
        # 使用最近10个数据点进行线性回归
        recent_data = list(self.progress_history)[-10:]
        if len(recent_data) < 2:
            return 0
            
        # 计算平均速度
        total_progress = recent_data[-1].progress - recent_data[0].progress
        total_time = recent_data[-1].timestamp - recent_data[0].timestamp
        
        if total_time <= 0 or total_progress <= 0:
            return 0
            
        avg_speed = total_progress / total_time
        if avg_speed <= 0:
            return 0
            
        remaining_progress = 1.0 - recent_data[-1].progress
        return remaining_progress / avg_speed

class ProgressManager:
    """进度管理器 - 协调多个文件的进度"""
    
    def __init__(self):
        self.active_parsers: Dict[str, FFmpegProgressParser] = {}
        self.callbacks: list[Callable] = []
        self.lock = threading.Lock()
        self.update_thread = None
        self.running = False
        self.update_interval = 0.1  # 100ms
        
    def start_monitoring(self, file_name: str, total_duration: float):
        """开始监控新文件"""
        with self.lock:
            parser = FFmpegProgressParser(total_duration, file_name)
            self.active_parsers[file_name] = parser
            
            if not self.running:
                self._start_update_thread()
    
    def stop_monitoring(self, file_name: str):
        """停止监控文件"""
        with self.lock:
            if file_name in self.active_parsers:
                del self.active_parsers[file_name]
                
            if not self.active_parsers and self.running:
                self._stop_update_thread()
    
    def parse_progress(self, file_name: str, line: str) -> Optional[ProgressData]:
        """解析进度数据"""
        with self.lock:
            if file_name not in self.active_parsers:
                return None
                
            parser = self.active_parsers[file_name]
            return parser.parse_line(line)
    
    def add_callback(self, callback: Callable[[ProgressData], None]):
        """添加进度回调"""
        with self.lock:
            self.callbacks.append(callback)
    
    def remove_callback(self, callback: Callable):
        """移除进度回调"""
        with self.lock:
            if callback in self.callbacks:
                self.callbacks.remove(callback)
    
    def _start_update_thread(self):
        """启动更新线程"""
        if self.update_thread is None or not self.update_thread.is_alive():
            self.running = True
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
    
    def _stop_update_thread(self):
        """停止更新线程"""
        self.running = False
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=1.0)
    
    def _update_loop(self):
        """更新循环"""
        while self.running:
            try:
                with self.lock:
                    # 获取所有活动的进度
                    for file_name, parser in list(self.active_parsers.items()):
                        if parser.progress_history:
                            latest = parser.progress_history[-1]
                            # 通知所有回调
                            for callback in self.callbacks:
                                try:
                                    callback(latest)
                                except Exception as e:
                                    logging.error(f"进度回调失败: {e}")
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                logging.error(f"进度更新循环错误: {e}")
                time.sleep(0.5)
    
    def get_overall_progress(self) -> Dict[str, Any]:
        """获取总体进度"""
        with self.lock:
            if not self.active_parsers:
                return {"progress": 0, "speed": 0, "eta": 0, "files": 0}
            
            total_progress = 0
            total_speed = 0
            total_eta = 0
            active_files = len(self.active_parsers)
            
            for parser in self.active_parsers.values():
                if parser.progress_history:
                    latest = parser.progress_history[-1]
                    total_progress += latest.progress
                    total_speed += latest.speed
                    total_eta += latest.eta
            
            return {
                "progress": total_progress / active_files,
                "speed": total_speed / active_files,
                "eta": total_eta / active_files,
                "files": active_files
            }

# 全局进度管理器实例
progress_manager = ProgressManager()