import os
import sys
from pathlib import Path

def get_base_dir():
    """获取程序的基础目录"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe
        return Path(sys._MEIPASS)
    else:
        # 如果是开发环境
        return Path(os.path.dirname(os.path.abspath(__file__)))

def get_ffmpeg_path():
    """获取ffmpeg可执行文件的路径"""
    base_dir = get_base_dir()
    ffmpeg_dir = base_dir / 'ffmpeg'
    if sys.platform == 'win32':
        return str(ffmpeg_dir / 'ffmpeg.exe')
    return 'ffmpeg'  # 在非Windows系统上使用系统的ffmpeg

def get_ffprobe_path():
    """获取ffprobe可执行文件的路径"""
    base_dir = get_base_dir()
    ffmpeg_dir = base_dir / 'ffmpeg'
    if sys.platform == 'win32':
        return str(ffmpeg_dir / 'ffprobe.exe')
    return 'ffprobe'  # 在非Windows系统上使用系统的ffprobe
