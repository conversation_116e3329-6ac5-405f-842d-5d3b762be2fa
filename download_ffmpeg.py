import os
import zipfile
import urllib.request
import shutil

def download_ffmpeg():
    """下载并解压ffmpeg"""
    # ffmpeg下载地址（使用Windows 64位版本）
    url = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
    zip_path = "ffmpeg.zip"
    
    print("正在下载ffmpeg...")
    urllib.request.urlretrieve(url, zip_path)
    
    print("正在解压文件...")
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall("temp_ffmpeg")
    
    # 创建ffmpeg目录
    os.makedirs("ffmpeg", exist_ok=True)
    
    # 复制需要的文件
    ffmpeg_dir = next(d for d in os.listdir("temp_ffmpeg") if d.startswith("ffmpeg"))
    shutil.copy(f"temp_ffmpeg/{ffmpeg_dir}/bin/ffmpeg.exe", "ffmpeg/ffmpeg.exe")
    shutil.copy(f"temp_ffmpeg/{ffmpeg_dir}/bin/ffprobe.exe", "ffmpeg/ffprobe.exe")
    
    # 清理临时文件
    os.remove(zip_path)
    shutil.rmtree("temp_ffmpeg")
    
    print("ffmpeg文件已准备完成！")

if __name__ == "__main__":
    download_ffmpeg()
