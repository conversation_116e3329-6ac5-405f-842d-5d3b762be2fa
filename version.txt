VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 1, 0, 0),
    prodvers=(1, 1, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo([
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u''),
         StringStruct(u'FileDescription', u'视频批量转换工具'),
         StringStruct(u'FileVersion', u'1.2.0'),
         StringStruct(u'InternalName', u'视频批量转换工具'),
         StringStruct(u'LegalCopyright', u''),
         StringStruct(u'OriginalFilename', u'视频批量转换工具.exe'),
         StringStruct(u'ProductName', u'视频批量转换工具'),
         StringStruct(u'ProductVersion', u'1.2.0')])
    ]),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
