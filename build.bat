@echo off
chcp 65001
echo 正在检查Python环境...

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未检测到Python，请确保已安装Python并添加到系统环境变量中。
    pause
    exit /b 1
)

echo 正在安装/更新必要的依赖...
pip install -U pyinstaller pyqt5 psutil pillow

echo 正在检查ffmpeg目录...
if not exist "ffmpeg" (
    echo ffmpeg目录不存在，正在尝试下载...
    python download_ffmpeg.py
    if %errorlevel% neq 0 (
        echo 下载ffmpeg失败！请手动下载并解压到ffmpeg目录。
        pause
        exit /b 1
    )
)

echo 正在更新版本信息...
set /p version=请输入版本号(默认为1.2.0): 
if "%version%"=="" set version=1.2.0

echo 创建version.txt文件...
(
echo VSVersionInfo(
echo   ffi=FixedFileInfo(
echo     filevers=^(1, 1, 0, 0^),
echo     prodvers=^(1, 1, 0, 0^),
echo     mask=0x3f,
echo     flags=0x0,
echo     OS=0x40004,
echo     fileType=0x1,
echo     subtype=0x0,
echo     date=^(0, 0^)
echo   ^),
echo   kids=[
echo     StringFileInfo([
echo       StringTable(
echo         u'040904B0',
echo         [StringStruct(u'CompanyName', u''^),
echo          StringStruct(u'FileDescription', u'视频批量转换工具'^),
echo          StringStruct(u'FileVersion', u'1.2.0'^),
echo          StringStruct(u'InternalName', u'视频批量转换工具'^),
echo          StringStruct(u'LegalCopyright', u''^),
echo          StringStruct(u'OriginalFilename', u'视频批量转换工具.exe'^),
echo          StringStruct(u'ProductName', u'视频批量转换工具'^),
echo          StringStruct(u'ProductVersion', u'1.2.0'^)]^)
echo     ]^),
echo     VarFileInfo([VarStruct(u'Translation', [2052, 1200]^)]^)
echo   ]
echo ^)
) > version.txt

echo 正在清理旧文件...
rd /s /q build dist 2>nul
echo 正在开始打包程序...
pyinstaller --noconfirm 视频批量转换工具.spec

if %errorlevel% neq 0 (
    echo 打包过程中发生错误！
    pause
    exit /b 1
)

echo 正在复制必要文件...
if not exist "dist" (
    echo 创建dist目录...
    md "dist"
)

echo 复制配置文件...
xcopy /y "config.json" "dist\" || (
    echo 复制config.json失败！
    pause
    exit /b 1
)

echo 复制使用说明...
xcopy /y "使用说明.md" "dist\" || (
    echo 复制使用说明.md失败！
    pause
    exit /b 1
)

echo 复制ffmpeg文件...
if exist "ffmpeg" (
    xcopy /y /i /e "ffmpeg" "dist\ffmpeg\" || (
        echo 复制ffmpeg文件夹失败！
        pause
        exit /b 1
    )
) else (
    echo ffmpeg目录不存在！
    pause
    exit /b 1
)

echo 创建输入输出目录...
md "dist\input" 2>nul
md "dist\output" 2>nul

echo 创建启动批处理文件...
echo @echo off > "dist\启动视频转换工具.bat"
echo start "" "视频批量转换工具.exe" >> "dist\启动视频转换工具.bat"

echo 检查文件完整性...
if not exist "dist\视频批量转换工具.exe" (
    echo 程序文件未生成！
    pause
    exit /b 1
)

echo.
echo 打包完成！
echo ===================================
echo 所有文件已生成到dist目录，包括:
echo   视频批量转换工具.exe (带图标)
echo   启动视频转换工具.bat (启动脚本)
echo   config.json (配置文件)
echo   使用说明.md (使用文档)
echo   input文件夹 (输入目录)
echo   output文件夹 (输出目录)
echo   ffmpeg文件夹 (转换工具)
echo ===================================
echo 使用说明：请将整个dist目录复制到目标电脑
pause
