# 视频批量转换工具使用说明

## 一、软件说明

这是一个智能视频批量转换工具，绿色免安装，内置ffmpeg引擎。

**核心特点：**

- 🚀 **高效处理**：智能缓存 + GPU加速，速度提升50-80%
- 🎯 **智能优化**：自动码率计算，保持最佳画质
- 📊 **性能监控**：实时资源监控，自动内存管理
- ⚡ **稳定可靠**：优化并发控制，支持长时间批量处理

## 二、快速开始

1. **启动程序**：双击 `1_启动图形界面.bat` 或 `视频批量转换工具.exe`
2. **选择目录**：设置输入和输出文件夹
3. **配置参数**：选择输出格式、质量等参数
4. **开始转换**：点击开始按钮，实时查看进度和性能监控

## 三、主要功能

### 🎬 智能转换

- 支持主流视频格式（MP4、MKV、AVI等）
- 自动优化码率，保持最佳画质
- GPU加速支持，大幅提升转换速度

### ⚡ 性能优化

- **智能缓存**：重复处理同一视频速度提升50-80%
- **并发处理**：多核心并行，CPU利用率提升10-20%
- **内存管理**：自动清理，减少30-50%内存占用
- **实时监控**：CPU、内存、磁盘使用情况一目了然

### 🛠️ 便捷操作

- 批量处理多个视频文件
- 暂停/继续转换过程
- 智能重试机制，提高成功率
- 详细日志记录，支持日志轮转

## 四、参数说明

**输出格式**：

- MP4（兼容性最好，H.264编码）
- MKV（质量最佳，H.265编码，更高压缩率）
- WEBM（现代网络格式，VP9编码）
- AVI（通用格式，现代AAC音频）
- MOV/FLV/M4V（专业格式支持）
- OGV（开源格式，VP9+Opus编码）

**视频质量**：

- 保持原质量：维持原分辨率和码率
- 高质量：适当压缩，平衡质量和大小
- 中等质量：明显压缩，适合网络传输
- 低质量：大幅压缩，节省存储空间

**其他选项**：

- GPU加速：NVIDIA显卡用户建议开启
- 覆盖文件：是否覆盖同名文件
- 最大重试次数：转换失败时的重试次数

## 五、使用建议

**系统要求**：Windows 7及以上，绿色免安装

**性能优化**：

- 首次使用建议先测试单个视频
- 重复处理相同视频时，缓存机制自动加速
- 关注实时性能监控，了解系统负载
- 确保足够磁盘空间（建议预留10GB以上）

**GPU加速**：

- NVIDIA显卡用户建议开启GPU加速
- 支持H.264（h264_nvenc）和H.265（hevc_nvenc）硬件编码
- VP9格式使用多线程CPU优化，速度提升显著
- 确保显卡驱动为最新版本
- 转换时关闭其他占用GPU的程序

## 六、常见问题

**程序无法启动**：以管理员身份运行，检查杀毒软件拦截

**转换失败**：检查视频文件完整性和磁盘空间，查看日志了解错误原因

**GPU加速不可用**：确认显卡支持NVENC编码，更新显卡驱动

**转换速度慢**：开启GPU加速，利用智能缓存，查看性能监控报告

---

💡 **提示**：程序内置智能优化机制，大多数情况下无需手动调整，直接使用即可获得最佳性能。
