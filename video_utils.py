import subprocess
import json
import logging
import hashlib
import time
from pathlib import Path
from ffmpeg_utils import get_ffmpeg_path, get_ffprobe_path

# 视频信息缓存
_video_info_cache = {}
_cache_max_size = 1000
_cache_ttl = 3600  # 缓存1小时

def get_video_info(video_file):
    """使用 ffprobe 获取视频文件的元数据，支持缓存机制。"""
    # 生成缓存键（基于文件路径和修改时间）
    try:
        file_stat = video_file.stat()
        cache_key = hashlib.md5(f"{video_file}_{file_stat.st_mtime}_{file_stat.st_size}".encode()).hexdigest()
        
        # 检查缓存
        current_time = time.time()
        if cache_key in _video_info_cache:
            cached_data, timestamp = _video_info_cache[cache_key]
            if current_time - timestamp < _cache_ttl:
                return cached_data
            else:
                # 缓存过期，删除
                del _video_info_cache[cache_key]
    except Exception:
        cache_key = None
    cmd = [
        get_ffprobe_path(),
        '-v', 'error',
        '-select_streams', 'v:0',
        '-show_entries', 'stream=width,height,bit_rate:format=duration,bit_rate',
        '-of', 'json',
        str(video_file)
    ]

    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
        
        # 增加更多的错误检查和日志记录
        if result.returncode != 0:
            logging.error(f"ffprobe 命令执行失败: {result.stderr}")
            return None

        try:
            info = json.loads(result.stdout)
        except json.JSONDecodeError:
            logging.error(f"解析 ffprobe 输出失败: {result.stdout}")
            return None

        # 检查 streams 和 format 是否存在
        if not info.get('streams') or not info.get('format'):
            logging.error(f"未找到视频流信息: {info}")
            return None

        stream = info['streams'][0]
        format_info = info['format']

        # 尝试从多个位置获取比特率
        bitrate = (
            stream.get('bit_rate') or  # 视频流比特率
            format_info.get('bit_rate') or  # 格式整体比特率
            '0'
        )

        video_info = {
            'width': stream.get('width', 0),
            'height': stream.get('height', 0),
            'bitrate': bitrate,
            'duration': float(format_info.get('duration', 0))
        }
        
        # 存储到缓存
        if cache_key:
            # 清理过期缓存
            _cleanup_cache()
            _video_info_cache[cache_key] = (video_info, time.time())
        
        return video_info
    except Exception as e:
        logging.error(f"获取 '{video_file.name}' 信息失败: {e}")
        return None

def _cleanup_cache():
    """清理过期和过多的缓存项。"""
    current_time = time.time()
    
    # 删除过期项
    expired_keys = []
    for key, (data, timestamp) in _video_info_cache.items():
        if current_time - timestamp > _cache_ttl:
            expired_keys.append(key)
    
    for key in expired_keys:
        del _video_info_cache[key]
    
    # 如果缓存仍然过大，删除最旧的项
    if len(_video_info_cache) > _cache_max_size:
        # 按时间戳排序，删除最旧的项
        sorted_items = sorted(_video_info_cache.items(), key=lambda x: x[1][1])
        items_to_remove = len(_video_info_cache) - _cache_max_size
        for i in range(items_to_remove):
            del _video_info_cache[sorted_items[i][0]]

def calculate_bitrate(original_bitrate, target_resolution, original_resolution, target_format):
    """根据目标分辨率、原始分辨率和目标格式计算新的比特率。"""
    try:
        # 确保 original_bitrate 是数字
        original_bitrate = float(original_bitrate) if original_bitrate else 0
        
        original_pixels = original_resolution[0] * original_resolution[1]
        target_pixels = target_resolution[0] * target_resolution[1]
        ratio = target_pixels / original_pixels
        
        # 根据目标格式调整压缩因子
        format_compression_factors = {
            'mp4': 0.8,    # H.264编码效率较高
            'mkv': 0.85,   # HEVC/H.265编码效率更高
            'avi': 0.75,   # 较旧的编码格式，压缩效率较低
            'mov': 0.8,    # 类似mp4，使用H.264
            'flv': 0.7,    # Flash视频，压缩效率较低
            'webm': 0.7,   # VP9编码，压缩效率较高
            'm4v': 0.8,    # 类似mp4
            'mpg': 0.6,    # MPEG-2编码，压缩效率较低
            'mpeg': 0.6,   # MPEG-2编码，压缩效率较低
            'ts': 0.75,    # 传输流，通常使用H.264
            'mts': 0.75,   # 类似ts
            'm2ts': 0.75,  # 类似ts
            'vob': 0.6,    # DVD视频，使用MPEG-2
            'divx': 0.7,   # 较旧的编码格式
            'ogv': 0.7,    # Theora编码
            '3gp': 0.6,    # 移动设备格式，压缩效率较低
            'asf': 0.7,    # 较旧的Windows媒体格式
            'rm': 0.65,    # RealMedia格式
            'rmvb': 0.7,   # RealMedia可变比特率
            'f4v': 0.75    # Flash视频
        }
        
        compression_factor = format_compression_factors.get(target_format.lower(), 0.8)
        
        # 计算新的码率，考虑分辨率和格式压缩因子
        new_bitrate = int(original_bitrate * ratio * compression_factor)
        
        # 设置最小和最大比特率限制，避免极端值
        min_bitrate = 500000  # 500 kbps
        max_bitrate = 20000000  # 20 Mbps
        
        if new_bitrate < min_bitrate:
            new_bitrate = min_bitrate
        elif new_bitrate > max_bitrate:
            new_bitrate = max_bitrate
            
        return new_bitrate
    except Exception as e:
        logging.error(f"计算比特率时发生错误: {e}")
        return None

def build_ffmpeg_command(video_file, config, output_file, video_info):
    """构建 FFmpeg 命令，用于视频转码。"""
    # 如果配置了保持原分辨率，则使用原始分辨率
    if config.get('keep_original_resolution', False):
        target_resolution = [video_info['width'], video_info['height']]
    else:
        target_resolution = list(map(int, config['resolution'].split('x')))
    
    original_resolution = [video_info['width'], video_info['height']]
    
    # 增加比特率计算的错误处理，考虑目标格式
    video_bitrate = calculate_bitrate(
        video_info['bitrate'], 
        target_resolution, 
        original_resolution, 
        config['video_format']
    )
    if video_bitrate is None:
        video_bitrate = int(config['video_bitrate'][:-1]) * 1000

    # 设置目标码率和允许的波动范围
    target_bitrate = f"{video_bitrate // 1000}k"
    minrate = f"{int(video_bitrate * 0.8) // 1000}k"
    maxrate = f"{int(video_bitrate * 1.2) // 1000}k"
    bufsize = f"{video_bitrate // 1000 * 2}k"

    command = [
        get_ffmpeg_path(),
        '-i', str(video_file),
        '-s', f"{target_resolution[0]}x{target_resolution[1]}",
        '-b:v', target_bitrate,
        '-minrate', minrate,
        '-maxrate', maxrate,
        '-bufsize', bufsize,
        '-b:a', config['audio_bitrate'],
    ]

    # 根据输出格式选择视频编码器和容器
    video_format = config['video_format'].lower()
    
    # 常见格式的编码器映射（优化版）
    format_codec_map = {
        'mp4': {'video': 'libx264', 'audio': 'aac', 'container': 'mp4'},
        'mkv': {'video': 'libx265', 'audio': 'aac', 'container': 'matroska'},  # 使用更高效的H.265
        'avi': {'video': 'libx264', 'audio': 'aac', 'container': 'avi'},  # 改用AAC音频
        'mov': {'video': 'libx264', 'audio': 'aac', 'container': 'mov'},
        'flv': {'video': 'libx264', 'audio': 'aac', 'container': 'flv'},
        'webm': {'video': 'libvpx-vp9', 'audio': 'libopus', 'container': 'webm'},
        'm4v': {'video': 'libx264', 'audio': 'aac', 'container': 'm4v'},
        'mpg': {'video': 'libx264', 'audio': 'aac', 'container': 'mpeg'},  # 使用现代编码器
        'mpeg': {'video': 'libx264', 'audio': 'aac', 'container': 'mpeg'},  # 使用现代编码器
        'ts': {'video': 'libx264', 'audio': 'aac', 'container': 'mpegts'},
        'mts': {'video': 'libx264', 'audio': 'aac', 'container': 'mpegts'},
        'm2ts': {'video': 'libx264', 'audio': 'aac', 'container': 'mpegts'},
        'vob': {'video': 'mpeg2video', 'audio': 'ac3', 'container': 'vob'},  # 保持兼容性
        'divx': {'video': 'libx264', 'audio': 'aac', 'container': 'avi'},  # 改用AAC音频
        'ogv': {'video': 'libvpx-vp9', 'audio': 'libopus', 'container': 'ogg'}  # 使用现代编码器
    }
    
    # 获取格式对应的编码器设置，如果不在映射表中则使用默认的H.264/AAC
    codec_settings = format_codec_map.get(video_format, {'video': 'libx264', 'audio': 'aac', 'container': video_format})
    
    command.extend([
        '-c:v', codec_settings['video']
    ])
    
    # 只有x264/x265编码器支持preset参数
    if codec_settings['video'] in ['libx264', 'libx265']:
        command.extend(['-preset', 'slow'])
    
    command.extend([
        '-c:a', codec_settings['audio'],
        '-f', codec_settings['container']
    ])

    # GPU 加速选项
    if config['use_gpu']:
        # 只有特定编码器支持NVIDIA GPU加速
        if codec_settings['video'] == 'libx264':
            command[command.index('-c:v') + 1] = 'h264_nvenc'
            command.extend([
                '-preset', 'slow',
                '-rc', 'vbr_hq',  # 使用高质量VBR模式
                '-cq', '0',  # 禁用CQ模式，使用指定的目标比特率
            ])
        elif codec_settings['video'] == 'libx265':
            command[command.index('-c:v') + 1] = 'hevc_nvenc'
            command.extend([
                '-preset', 'slow',
                '-rc', 'vbr_hq',  # 使用高质量VBR模式
                '-cq', '0',  # 禁用CQ模式，使用指定的目标比特率
            ])
        elif codec_settings['video'] == 'libvpx-vp9':
            # VP9编码器优化参数，提升编码速度
            command.extend([
                '-speed', '2',  # 加快编码速度（0-8，数值越大速度越快但质量略降）
                '-tile-columns', '2',  # 启用并行编码
                '-frame-parallel', '1',  # 启用帧并行处理
                '-threads', '8',  # 使用多线程
                '-row-mt', '1'  # 启用行级多线程
            ])
    else:
        # CPU编码优化参数
        if codec_settings['video'] == 'libx265':
            command.extend([
                '-x265-params', 'pools=8:frame-threads=4'  # 多线程优化
            ])

    command.extend([
        '-y' if config['overwrite'] else '-n',
        str(output_file)
    ])
    
    return command
