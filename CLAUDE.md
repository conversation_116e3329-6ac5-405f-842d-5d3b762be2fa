# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概览

这是一个智能视频批量转换工具，绿色免安装，内置ffmpeg引擎。核心特点包括：
- **高效处理**：智能缓存 + GPU加速，速度提升50-80%
- **智能优化**：自动码率计算，保持最佳画质
- **性能监控**：实时资源监控，自动内存管理
- **稳定可靠**：优化并发控制，支持长时间批量处理

支持主流视频格式（MP4、MKV、AVI等），具备完整的图形界面和命令行版本。

## 架构设计

### 核心架构模式
- **MVC模式**: GUI采用模型-视图-控制器架构，PyQt5负责界面展示，逻辑处理分离
- **生产者-消费者模式**: 视频转换任务队列处理，支持异步执行
- **缓存模式**: 视频元数据智能缓存，提升重复处理性能
- **监控模式**: 实时性能监控和资源使用统计

### 技术栈
- **Python 3.6+**: 核心语言
- **PyQt5**: 图形界面框架（中文界面，现代化UI设计）
- **ffmpeg**: 视频处理引擎（内置，无需安装）
- **psutil**: 系统监控和进程管理
- **concurrent.futures**: 并发处理框架
- **Pillow**: 图像处理支持
- **PyInstaller**: 打包工具

## 核心模块架构

### 1. 界面层 (gui.py)
- **VideoConverterGUI**: 主窗口类，负责界面展示和用户交互
- **VideoConverterThread**: 工作线程类，实现异步转换和进度更新
- **信号机制**: 使用pyqtSignal实现线程间通信

### 2. 业务逻辑层 (script.py)
- **转换引擎**: 核心视频处理逻辑
- **并发控制**: 基于ThreadPoolExecutor的并发管理
- **错误处理**: 智能重试机制和错误恢复
- **资源管理**: 进程生命周期管理和内存清理

### 3. 工具层
- **video_utils.py**: 视频元数据获取和命令构建
- **ffmpeg_utils.py**: 跨平台ffmpeg路径管理
- **performance_monitor.py**: 实时性能监控和数据收集
- **progress_monitor.py**: 高精度进度跟踪和性能统计

### 4. 配置管理
- **config.json**: 统一配置管理
- **动态配置**: 运行时参数调整
- **持久化**: 用户偏好记忆

## 关键数据结构

### 缓存机制
```python
_video_info_cache = {}  # 视频信息缓存
_cache_max_size = 1000  # 最大缓存条目
_cache_ttl = 3600      # 缓存有效期（秒）
```

### 并发控制
```python
# 线程池配置
max_workers = psutil.cpu_count(logical=False)  # 物理核心数
executor = ThreadPoolExecutor(max_workers=max_workers)

# 进度更新队列
progress_update_queue = deque(maxlen=100)
PROGRESS_UPDATE_INTERVAL = 0.1  # 100ms更新间隔
```

### 性能监控
```python
# 监控数据存储
cpu_usage = deque(maxlen=1000)
memory_usage = deque(maxlen=1000)
disk_io = deque(maxlen=1000)
timestamps = deque(maxlen=1000)
```

## 常用命令

### 开发环境
```bash
# 运行GUI版本
python gui.py

# 运行命令行版本
python script.py --input_dir ./input --output_dir ./output --video_format mp4

# 性能测试
python -c "from performance_monitor import start_performance_monitoring; start_performance_monitoring()"
```

### 构建发布
```bash
# 完整构建流程
build.bat

# 手动PyInstaller打包
pyinstaller --noconfirm 视频批量转换工具.spec

# 清理构建缓存
rd /s /q build dist
```

### 调试命令
```bash
# 检查依赖
python -c "import script; script.check_dependencies()"

# 测试视频信息获取
python -c "from video_utils import get_video_info; print(get_video_info(Path('test.mp4')))"

# 查看性能报告
python -c "from performance_monitor import get_performance_report; print(get_performance_report())"
```

## 配置系统

### 配置文件结构 (config.json)
```json
{
    "input_dir": "输入目录路径",
    "output_dir": "输出目录路径", 
    "video_format": "输出格式(mp4/mkv/avi)",
    "resolution": "目标分辨率",
    "video_bitrate": "视频码率",
    "audio_bitrate": "音频码率",
    "use_gpu": true/false,
    "max_retries": 1,
    "overwrite": true/false,
    "supported_formats": ["主流视频格式列表"],
    "output_name": "{name}_{resolution}_{timestamp}",
    "keep_original_resolution": true/false,
    "remember_dirs": true/false,
    "auto_detect_videos": true/false,
    "min_size": 0,
    "max_size": 10240
}
```

### 视频质量选项
- **保持原质量**：维持原分辨率和码率
- **高质量**：适当压缩，平衡质量和大小
- **中等质量**：明显压缩，适合网络传输
- **低质量**：大幅压缩，节省存储空间

### GPU加速配置
- **自动检测**：NVIDIA显卡用户建议开启
- **编码器映射**：支持h264_nvenc等多种GPU编码器
- **驱动要求**：确保显卡驱动为最新版本

## 性能优化要点

### 缓存优化
- 视频信息缓存减少50-80%重复I/O操作
- 基于文件路径、修改时间和大小的智能缓存键
- 自动清理过期和过多缓存项

### 并发优化
- CPU核心数自适应线程池配置
- 移除双重并发控制，简化逻辑
- 批量进度更新减少GUI阻塞

### 内存管理
- 日志轮转机制（最大10MB，保留3个备份）
- 定期垃圾回收和资源清理
- 限制日志缓冲区大小避免内存泄漏

### 错误处理
- 指数退避重试策略（最大延迟10秒）
- 详细的错误分类和追踪
- 进程级暂停/恢复机制

## 扩展开发指南

### 添加新格式支持
1. 在config.json的supported_formats中添加新格式
2. 在video_utils.py中更新格式映射
3. 测试ffmpeg命令兼容性

### 集成新功能
1. 在gui.py中添加UI控件
2. 在script.py中实现业务逻辑
3. 更新配置文件结构
4. 添加相应的错误处理和日志

### 性能监控扩展
1. 在performance_monitor.py中添加新的监控指标
2. 更新get_performance_report()方法
3. 在GUI中添加对应的展示组件

## 部署和使用指南

### 快速开始
1. **启动程序**：双击 `1_启动图形界面.bat` 或 `视频批量转换工具.exe`
2. **选择目录**：设置输入和输出文件夹
3. **配置参数**：选择输出格式、质量等参数
4. **开始转换**：点击开始按钮，实时查看进度和性能监控

### 系统要求
- **操作系统**：Windows 7及以上，绿色免安装
- **内存要求**：至少2GB内存
- **磁盘空间**：建议预留10GB以上用于输出文件
- **显卡要求**：NVIDIA显卡用户可开启GPU加速（需最新驱动）

### 文件结构
```text
dist/
├── 视频批量转换工具.exe    # 主程序
├── 启动视频转换工具.bat   # 启动脚本
├── config.json            # 配置文件
├── 使用说明.md            # 使用文档
├── input/                 # 输入目录
├── output/                # 输出目录
└── ffmpeg/                # 转换工具
    ├── ffmpeg.exe
    └── ffprobe.exe
```

### 最佳实践
1. **首次使用**：建议先测试单个视频了解流程
2. **大批量处理**：充分利用缓存机制，重复处理相同文件速度提升50-80%
3. **性能优化**：关注实时性能监控，根据系统负载调整并发数量
4. **GPU加速**：NVIDIA显卡用户建议开启，转换时关闭其他占用GPU的程序

### 故障排除
- **程序无法启动**：以管理员身份运行，检查杀毒软件拦截
- **转换失败**：检查视频文件完整性和磁盘空间，查看日志了解错误原因
- **GPU加速不可用**：确认显卡支持NVENC编码，更新显卡驱动
- **转换速度慢**：开启GPU加速，利用智能缓存，查看性能监控报告

### 部署配置
- **PyInstaller打包**：完整包含所有依赖，生成便携式绿色软件
- **自动更新**：支持配置文件热重载，无需重启程序
- **版本管理**：内置版本信息，支持自动版本号更新
- **跨平台**：已配置Windows专用路径和编码器映射